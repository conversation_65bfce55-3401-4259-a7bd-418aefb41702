<?php
// 先处理逻辑，再包含header
require_once 'includes/functions.php';

$productId = intval($_GET['id'] ?? 0);

if (!$productId) {
    redirect('index.php');
}

// 获取商品详情
$product = getProduct($productId);

if (!$product) {
    $_SESSION['error_message'] = '商品不存在';
    redirect('index.php');
}

// 增加浏览次数
incrementViews($productId);

// 记录浏览历史（使用新的函数）
addBrowseHistory($productId);

$pageTitle = htmlspecialchars($product['title']);
$images = json_decode($product['images'], true);
if ($images) {
    // 为图片添加路径前缀
    $images = array_map(function($img) {
        return UPLOAD_PATH . $img;
    }, $images);
} else {
    $images = ['images/product-default.svg'];
}

require_once 'includes/header.php';

// 获取相似商品
$similarProducts = getProducts(4, 0, null, null);

// 检查用户是否已收藏该商品
$isFavorited = false;
if (isLoggedIn()) {
    $stmt = $pdo->prepare("SELECT id FROM favorites WHERE user_id = ? AND product_id = ?");
    $stmt->execute([$_SESSION['user_id'], $productId]);
    $isFavorited = $stmt->fetch() !== false;
}
?>

<!-- 主要内容区 -->
<main class="main detail-page">
    <div class="container">
        <!-- 商品详情 -->
        <div class="product-detail">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="index.php">首页</a> &gt; 
                <a href="list.php?category=<?php echo $product['category_name']; ?>"><?php echo htmlspecialchars($product['category_name']); ?></a> &gt; 
                <span class="current-category"><?php echo htmlspecialchars($product['title']); ?></span>
            </div>

            <!-- 商品画廊 -->
            <div class="product-gallery">
                <div class="gallery-main">
                    <div class="gallery-main-img">
                        <img src="<?php echo $images[0]; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                        <div class="image-zoom-hint">
                            <i class="bi bi-zoom-in"></i>
                            <span>点击查看大图</span>
                        </div>
                    </div>
                </div>
                <div class="gallery-thumbs">
                    <?php foreach ($images as $index => $image): ?>
                        <div class="gallery-thumb <?php echo $index === 0 ? 'active' : ''; ?>">
                            <img src="<?php echo $image; ?>" alt="商品图片<?php echo $index + 1; ?>">
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- 商品信息 -->
            <div class="product-header">
                <h1><?php echo htmlspecialchars($product['title']); ?></h1>
                <div class="product-price-info">
                    <span class="detail-price"><?php echo formatPrice($product['price']); ?></span>
                    <?php if ($product['original_price']): ?>
                        <span class="detail-original-price"><?php echo formatPrice($product['original_price']); ?></span>
                    <?php endif; ?>
                </div>
                <div class="product-meta-info">
                    <?php if ($product['is_virtual']): ?>
                        <div class="meta-item">
                            <i class="bi bi-lightning-charge"></i>
                            <span>即时交付</span>
                        </div>
                    <?php endif; ?>
                    <div class="meta-item">
                        <i class="bi bi-shield-check"></i>
                        <span>担保交易</span>
                    </div>
                    <div class="meta-item">
                        <i class="bi bi-eye"></i>
                        <span>浏览 <?php echo $product['views']; ?> 次</span>
                    </div>
                    <div class="meta-item">
                        <i class="bi bi-calendar3"></i>
                        <span>发布于 <?php echo date('Y-m-d', strtotime($product['created_at'])); ?></span>
                    </div>
                </div>
            </div>

            <!-- 商品描述 -->
            <div class="product-description">
                <h2 class="description-title">商品描述</h2>
                <div class="description-content">
                    <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                </div>
            </div>

            <!-- 虚拟商品属性 -->
            <?php if ($product['is_virtual'] && $product['usage_period']): ?>
                <div class="product-attributes">
                    <h2 class="description-title">商品属性</h2>
                    <div class="attributes-list">
                        <div class="attribute-item">
                            <div class="attribute-label">商品类型</div>
                            <div class="attribute-value">虚拟商品</div>
                        </div>
                        <div class="attribute-item">
                            <div class="attribute-label">使用期限</div>
                            <div class="attribute-value">
                                <?php
                                if ($product['usage_period'] === 'unlimited') {
                                    echo '永久有效';
                                } else {
                                    echo $product['period_value'] . ' ' .
                                         ($product['period_unit'] === 'day' ? '天' :
                                          ($product['period_unit'] === 'month' ? '个月' : '年'));
                                }
                                ?>
                            </div>
                        </div>
                        <div class="attribute-item">
                            <div class="attribute-label">发货方式</div>
                            <div class="attribute-value">
                                <?php echo $product['delivery_method'] === 'automatic' ? '自动发货' : '手动发货'; ?>
                            </div>
                        </div>
                        <?php if ($product['platform']): ?>
                            <div class="attribute-item">
                                <div class="attribute-label">使用平台</div>
                                <div class="attribute-value"><?php echo htmlspecialchars($product['platform']); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- 购买后显示内容 -->
            <div class="purchase-content-preview">
                <h2 class="description-title">
                    <i class="bi bi-gift"></i>
                    购买后您将获得
                </h2>
                <div class="preview-content">
                    <?php if ($product['is_virtual']): ?>
                        <div class="preview-item">
                            <div class="preview-icon">
                                <i class="bi bi-download"></i>
                            </div>
                            <div class="preview-info">
                                <h4>数字商品内容</h4>
                                <p>购买成功后，您将立即获得商品的下载链接或激活码</p>
                            </div>
                        </div>
                        <div class="preview-item">
                            <div class="preview-icon">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <div class="preview-info">
                                <h4>使用说明</h4>
                                <p>详细的使用教程和注意事项，确保您能正确使用商品</p>
                            </div>
                        </div>
                        <?php if ($product['delivery_method'] === 'automatic'): ?>
                            <div class="preview-item">
                                <div class="preview-icon">
                                    <i class="bi bi-lightning-charge"></i>
                                </div>
                                <div class="preview-info">
                                    <h4>即时发货</h4>
                                    <p>支付完成后系统将自动发货，无需等待</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="preview-item">
                            <div class="preview-icon">
                                <i class="bi bi-box-seam"></i>
                            </div>
                            <div class="preview-info">
                                <h4>实物商品</h4>
                                <p>卖家将按照约定的方式为您发货</p>
                            </div>
                        </div>
                        <div class="preview-item">
                            <div class="preview-icon">
                                <i class="bi bi-truck"></i>
                            </div>
                            <div class="preview-info">
                                <h4>物流信息</h4>
                                <p>您可以在订单页面查看物流跟踪信息</p>
                            </div>
                        </div>
                    <?php endif; ?>
                    <div class="preview-item">
                        <div class="preview-icon">
                            <i class="bi bi-headset"></i>
                        </div>
                        <div class="preview-info">
                            <h4>售后服务</h4>
                            <p>如有问题可联系卖家或平台客服获得帮助</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易按钮 -->
            <div class="trade-actions">
                <div class="main-actions">
                    <?php if (isLoggedIn() && $_SESSION['user_id'] != $product['user_id']): ?>
                        <button class="btn btn-primary btn-large" onclick="buyNow(<?php echo $product['id']; ?>)">
                            <i class="bi bi-lightning-charge"></i>
                            <span>立即购买</span>
                        </button>
                        <button class="btn btn-secondary btn-large" onclick="addToCart(<?php echo $product['id']; ?>)">
                            <i class="bi bi-cart-plus"></i>
                            <span>加入购物车</span>
                        </button>
                    <?php elseif (!isLoggedIn()): ?>
                        <a href="login.php" class="btn btn-primary btn-large">
                            <i class="bi bi-person-check"></i>
                            <span>登录后购买</span>
                        </a>
                    <?php else: ?>
                        <button class="btn btn-outline btn-large" disabled>
                            <i class="bi bi-person-gear"></i>
                            <span>这是您发布的商品</span>
                        </button>
                    <?php endif; ?>
                </div>
                <div class="secondary-actions">
                    <?php if (isLoggedIn() && $_SESSION['user_id'] != $product['user_id']): ?>
                        <button class="action-btn <?php echo $isFavorited ? 'favorited' : ''; ?>" onclick="toggleFavorite(<?php echo $product['id']; ?>)" title="<?php echo $isFavorited ? '取消收藏' : '收藏商品'; ?>">
                            <i class="bi bi-heart<?php echo $isFavorited ? '-fill' : ''; ?>"></i>
                            <span><?php echo $isFavorited ? '已收藏' : '收藏'; ?></span>
                        </button>
                    <?php endif; ?>
                    <button class="action-btn" onclick="shareProduct()" title="分享商品">
                        <i class="bi bi-share"></i>
                        <span>分享</span>
                    </button>
                    <button class="action-btn" onclick="contactSeller()" title="联系卖家">
                        <i class="bi bi-chat-dots"></i>
                        <span>咨询</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 卖家信息 -->
            <div class="seller-card">
                <div class="seller-profile">
                    <img src="<?php echo $product['seller_avatar']; ?>" alt="卖家头像" class="seller-profile-avatar">
                    <div class="seller-profile-info">
                        <h3><?php echo htmlspecialchars($product['seller_name']); ?></h3>
                        <div class="seller-badges">
                            <span class="seller-badge"><i class="bi bi-star"></i> 评分 <?php echo $product['seller_rating']; ?></span>
                        </div>
                    </div>
                </div>
                <div class="seller-stats">
                    <div class="stat-item">
                        <span class="stat-value"><?php echo number_format($product['seller_rating'], 1); ?></span>
                        <span class="stat-label">评分</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $product['total_sales'] ?? 0; ?></span>
                        <span class="stat-label">已卖出</span>
                    </div>
                </div>
                <?php if (isLoggedIn() && $_SESSION['user_id'] != $product['user_id']): ?>
                    <a href="#" class="contact-seller-btn">联系卖家</a>
                <?php endif; ?>
            </div>

            <!-- 相似商品推荐 -->
            <div class="similar-products">
                <h3>相似商品推荐</h3>
                <?php foreach ($similarProducts as $similar): ?>
                    <div class="similar-product-item">
                        <a href="detail.php?id=<?php echo $similar['id']; ?>">
                            <div class="similar-product-img">
                                <?php
                                $similarImages = json_decode($similar['images'], true);
                                $similarImage = $similarImages ? (UPLOAD_PATH . $similarImages[0]) : 'images/product-default.svg';
                                ?>
                                <img src="<?php echo $similarImage; ?>" alt="<?php echo htmlspecialchars($similar['title']); ?>">
                            </div>
                            <div class="similar-product-info">
                                <h4><?php echo htmlspecialchars(mb_substr($similar['title'], 0, 30)); ?></h4>
                                <div class="similar-product-price"><?php echo formatPrice($similar['price']); ?></div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</main>

<!-- 购买确认模态框 -->
<div id="purchaseModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>确认购买</h3>
            <span class="close" onclick="closePurchaseModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="purchase-product-info">
                <img id="purchaseProductImage" src="" alt="商品图片" class="purchase-product-img">
                <div class="purchase-product-details">
                    <h4 id="purchaseProductTitle"></h4>
                    <div class="purchase-product-price" id="purchaseProductPrice"></div>
                    <?php if ($product['is_virtual']): ?>
                        <div class="purchase-virtual-info">
                            <div class="virtual-info-item">
                                <i class="bi bi-lightning-charge"></i>
                                <span>虚拟商品，购买后即时发货</span>
                            </div>
                            <?php if ($product['usage_period'] !== 'unlimited'): ?>
                                <div class="virtual-info-item">
                                    <i class="bi bi-clock"></i>
                                    <span>有效期：<?php echo $product['period_value'] . ' ' .
                                        ($product['period_unit'] === 'day' ? '天' :
                                         ($product['period_unit'] === 'month' ? '个月' : '年')); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="purchase-quantity">
                <label>购买数量：</label>
                <div class="quantity-controls">
                    <button type="button" onclick="decreaseQuantity()">-</button>
                    <input type="number" id="purchaseQuantity" value="1" min="1" max="<?php echo $product['stock']; ?>">
                    <button type="button" onclick="increaseQuantity()">+</button>
                </div>
                <span class="stock-info">库存：<?php echo $product['stock']; ?> 件</span>
            </div>
            <div class="purchase-total">
                <span class="total-label">总价：</span>
                <span class="total-price" id="totalPrice"><?php echo formatPrice($product['price']); ?></span>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closePurchaseModal()">取消</button>
            <button class="btn btn-primary" onclick="confirmPurchase()">确认购买</button>
        </div>
    </div>
</div>

<script>
function buyNow(productId) {
    // 显示购买确认模态框
    showPurchaseModal(productId);
}

function addToCart(productId) {
    // 实现加入购物车功能
    const formData = new FormData();
    formData.append('action', 'add');
    formData.append('product_id', productId);
    formData.append('quantity', 1);

    fetch('api/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('已加入购物车', 'success');
            updateCartCount();
        } else {
            showMessage(data.message || '加入购物车失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

function showPurchaseModal(productId) {
    // 设置商品信息
    document.getElementById('purchaseProductImage').src = '<?php echo $images[0]; ?>';
    document.getElementById('purchaseProductTitle').textContent = '<?php echo htmlspecialchars($product['title']); ?>';
    document.getElementById('purchaseProductPrice').textContent = '<?php echo formatPrice($product['price']); ?>';

    // 重置数量
    document.getElementById('purchaseQuantity').value = 1;
    updateTotalPrice();

    // 显示模态框
    document.getElementById('purchaseModal').style.display = 'block';
}

function closePurchaseModal() {
    document.getElementById('purchaseModal').style.display = 'none';
}

function increaseQuantity() {
    const quantityInput = document.getElementById('purchaseQuantity');
    const maxStock = parseInt(quantityInput.getAttribute('max'));
    const currentValue = parseInt(quantityInput.value);

    if (currentValue < maxStock) {
        quantityInput.value = currentValue + 1;
        updateTotalPrice();
    }
}

function decreaseQuantity() {
    const quantityInput = document.getElementById('purchaseQuantity');
    const currentValue = parseInt(quantityInput.value);

    if (currentValue > 1) {
        quantityInput.value = currentValue - 1;
        updateTotalPrice();
    }
}

function updateTotalPrice() {
    const quantity = parseInt(document.getElementById('purchaseQuantity').value);
    const unitPrice = <?php echo $product['price']; ?>;
    const totalPrice = quantity * unitPrice;

    document.getElementById('totalPrice').textContent = '¥' + totalPrice.toFixed(2);
}

function confirmPurchase() {
    const quantity = parseInt(document.getElementById('purchaseQuantity').value);
    const productId = <?php echo $product['id']; ?>;

    // 发送购买请求
    const formData = new FormData();
    formData.append('product_id', productId);
    formData.append('quantity', quantity);
    formData.append('action', 'buy_now');

    fetch('api/purchase.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('购买成功！', 'success');
            closePurchaseModal();
            // 可以跳转到订单页面
            setTimeout(() => {
                window.location.href = 'my-orders.php';
            }, 1500);
        } else {
            showMessage(data.message || '购买失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

function toggleFavorite(productId) {
    // 实现收藏功能
    fetch('api/favorite.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({product_id: productId})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // 更新收藏按钮状态
            const favoriteBtn = document.querySelector('button[onclick="toggleFavorite(' + productId + ')"]');
            if (data.action === 'added') {
                favoriteBtn.innerHTML = '<i class="bi bi-heart-fill"></i> 已收藏';
                favoriteBtn.classList.add('favorited');
            } else {
                favoriteBtn.innerHTML = '<i class="bi bi-heart"></i> 收藏';
                favoriteBtn.classList.remove('favorited');
            }
        } else {
            showMessage(data.message || '操作失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

function shareProduct() {
    // 实现分享功能
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($product['title']); ?>',
            url: window.location.href
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
            showMessage('链接已复制到剪贴板', 'success');
        }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = window.location.href;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showMessage('链接已复制到剪贴板', 'success');
        });
    }
}

function contactSeller() {
    // 实现联系卖家功能
    <?php if (isLoggedIn()): ?>
        // 这里可以打开聊天窗口或跳转到消息页面
        showMessage('联系卖家功能开发中...', 'info');
    <?php else: ?>
        showMessage('请先登录后再联系卖家', 'error');
        setTimeout(() => {
            window.location.href = 'login.php';
        }, 1500);
    <?php endif; ?>
}

function showMessage(message, type = 'info') {
    // 创建消息提示
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-toast message-${type}`;
    messageDiv.textContent = message;

    // 添加到页面
    document.body.appendChild(messageDiv);

    // 显示动画
    setTimeout(() => {
        messageDiv.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        messageDiv.classList.remove('show');
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

function updateCartCount() {
    // 更新购物车数量显示
    const formData = new FormData();
    formData.append('action', 'count');

    fetch('api/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const cartCountElement = document.querySelector('.cart-count');
            if (cartCountElement) {
                cartCountElement.textContent = data.count;
                cartCountElement.style.display = data.count > 0 ? 'inline' : 'none';
            }
        }
    })
    .catch(error => {
        console.error('Error updating cart count:', error);
    });
}

// 图片画廊功能
document.addEventListener('DOMContentLoaded', function() {
    // 图片缩略图点击事件
    document.querySelectorAll('.gallery-thumb').forEach(thumb => {
        thumb.addEventListener('click', function() {
            document.querySelectorAll('.gallery-thumb').forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            const img = this.querySelector('img');
            document.querySelector('.gallery-main-img img').src = img.src;
        });
    });

    // 主图点击放大功能
    document.querySelector('.gallery-main-img').addEventListener('click', function() {
        const img = this.querySelector('img');
        showImagePreview(img.src);
    });

    // 模态框点击外部关闭
    document.getElementById('purchaseModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closePurchaseModal();
        }
    });

    // 数量输入框变化事件
    document.getElementById('purchaseQuantity').addEventListener('input', updateTotalPrice);

    // 初始化购物车数量
    updateCartCount();
});

// 图片预览功能
function showImagePreview(imageSrc) {
    // 创建预览覆盖层
    const overlay = document.createElement('div');
    overlay.className = 'image-preview-overlay';
    overlay.innerHTML = `
        <div class="image-preview-container">
            <img src="${imageSrc}" alt="商品图片预览" class="preview-image">
            <button class="preview-close" onclick="closeImagePreview()">&times;</button>
        </div>
    `;

    document.body.appendChild(overlay);
    document.body.style.overflow = 'hidden';

    // 点击覆盖层关闭
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            closeImagePreview();
        }
    });

    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeImagePreview();
        }
    });
}

function closeImagePreview() {
    const overlay = document.querySelector('.image-preview-overlay');
    if (overlay) {
        overlay.remove();
        document.body.style.overflow = '';
    }
}
</script>

<style>
/* 详情页面优化样式 */

/* 商品画廊优化 */
.product-gallery {
    position: relative;
}

.gallery-main {
    position: relative;
    margin-bottom: 15px;
}

.gallery-main-img {
    position: relative;
    width: 100%;
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gallery-main-img:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gallery-main-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-main-img:hover img {
    transform: scale(1.05);
}

.image-zoom-hint {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-main-img:hover .image-zoom-hint {
    opacity: 1;
}

.gallery-thumbs {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding: 5px 0;
}

.gallery-thumb {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.gallery-thumb:hover {
    border-color: #ff6b35;
    transform: translateY(-2px);
}

.gallery-thumb.active {
    border-color: #ff6b35;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.gallery-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 交易按钮优化 */
.trade-actions {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 25px;
    margin: 25px 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.main-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.btn-large {
    flex: 1;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    min-height: 56px;
}

.btn-primary.btn-large {
    background: linear-gradient(135deg, #ff6b35, #ff5722);
    border: none;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.btn-primary.btn-large:hover {
    background: linear-gradient(135deg, #ff5722, #e64a19);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.btn-secondary.btn-large {
    background: linear-gradient(135deg, #fff, #f8f9fa);
    border: 2px solid #ff6b35;
    color: #ff6b35;
}

.btn-secondary.btn-large:hover {
    background: linear-gradient(135deg, #ff6b35, #ff5722);
    color: white;
    transform: translateY(-2px);
}

.secondary-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 10px 15px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    min-width: 60px;
}

.action-btn:hover {
    background: #f8f9fa;
    color: #ff6b35;
    transform: translateY(-2px);
}

.action-btn.favorited {
    color: #ff6b35;
}

.action-btn i {
    font-size: 20px;
}

.action-btn span {
    font-size: 12px;
    font-weight: 500;
}

/* 购买后显示内容样式 */
.purchase-content-preview {
    background: linear-gradient(135deg, #f8f9ff 0%, #fff5f0 100%);
    border-radius: 16px;
    padding: 30px;
    margin: 30px 0;
    border: 1px solid #e3f2fd;
}

.purchase-content-preview .description-title {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #333;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ff6b35;
}

.purchase-content-preview .description-title i {
    color: #ff6b35;
    font-size: 24px;
}

.preview-content {
    display: grid;
    gap: 20px;
}

.preview-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.preview-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.preview-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff6b35, #ff5722);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.preview-info h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.preview-info p {
    margin: 0;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

/* 图片预览样式 */
.image-preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
}

.image-preview-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.preview-close {
    position: absolute;
    top: -40px;
    right: -40px;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.preview-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 购买模态框样式 */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
    transition: color 0.2s;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 24px;
}

.purchase-product-info {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.purchase-product-img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #eee;
}

.purchase-product-details h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}

.purchase-product-price {
    font-size: 18px;
    font-weight: 700;
    color: #ff6b35;
    margin-bottom: 12px;
}

.purchase-virtual-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.virtual-info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #666;
}

.virtual-info-item i {
    color: #4CAF50;
}

.purchase-quantity {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
}

.quantity-controls button {
    background: #f8f9fa;
    border: none;
    width: 32px;
    height: 32px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
}

.quantity-controls button:hover {
    background: #e9ecef;
}

.quantity-controls input {
    border: none;
    width: 60px;
    height: 32px;
    text-align: center;
    font-size: 14px;
}

.stock-info {
    font-size: 13px;
    color: #666;
}

.purchase-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
    font-size: 16px;
}

.total-price {
    font-size: 20px;
    font-weight: 700;
    color: #ff6b35;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.message-toast.show {
    transform: translateX(0);
}

.message-success {
    background-color: #4CAF50;
}

.message-error {
    background-color: #f44336;
}

.message-info {
    background-color: #2196F3;
}

/* 收藏按钮状态 */
.btn.favorited {
    background-color: #ff6b35;
    color: white;
    border-color: #ff6b35;
}

.btn.favorited:hover {
    background-color: #e55a2b;
    border-color: #e55a2b;
}

/* 响应式优化 */
@media (max-width: 992px) {
    .main-actions {
        flex-direction: column;
    }

    .secondary-actions {
        justify-content: space-around;
    }

    .gallery-main-img {
        height: 300px;
    }

    .preview-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }

    .purchase-product-info {
        flex-direction: column;
        text-align: center;
    }

    .purchase-quantity {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
    }

    .trade-actions {
        padding: 20px;
        margin: 20px 0;
    }

    .btn-large {
        padding: 12px 20px;
        font-size: 15px;
        min-height: 48px;
    }

    .secondary-actions {
        gap: 15px;
    }

    .action-btn {
        min-width: 50px;
        padding: 8px 10px;
    }

    .action-btn i {
        font-size: 18px;
    }

    .action-btn span {
        font-size: 11px;
    }

    .gallery-thumbs {
        justify-content: center;
    }

    .gallery-thumb {
        width: 50px;
        height: 50px;
    }

    .purchase-content-preview {
        padding: 20px;
        margin: 20px 0;
    }

    .preview-item {
        padding: 15px;
    }

    .preview-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .preview-info h4 {
        font-size: 15px;
    }

    .preview-info p {
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .gallery-main-img {
        height: 250px;
    }

    .trade-actions {
        padding: 15px;
    }

    .btn-large {
        padding: 10px 15px;
        font-size: 14px;
        min-height: 44px;
    }

    .secondary-actions {
        gap: 10px;
    }

    .action-btn {
        min-width: 45px;
        padding: 6px 8px;
    }

    .purchase-content-preview .description-title {
        font-size: 18px;
        flex-direction: column;
        text-align: center;
        gap: 5px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
