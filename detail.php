<?php
// 先处理逻辑，再包含header
require_once 'includes/functions.php';

$productId = intval($_GET['id'] ?? 0);

if (!$productId) {
    redirect('index.php');
}

// 获取商品详情
$product = getProduct($productId);

if (!$product) {
    $_SESSION['error_message'] = '商品不存在';
    redirect('index.php');
}

// 增加浏览次数
incrementViews($productId);

// 记录浏览历史（使用新的函数）
addBrowseHistory($productId);

$pageTitle = htmlspecialchars($product['title']);
$images = json_decode($product['images'], true);
if ($images) {
    // 为图片添加路径前缀
    $images = array_map(function($img) {
        return UPLOAD_PATH . $img;
    }, $images);
} else {
    $images = ['images/product-default.svg'];
}

require_once 'includes/header.php';

// 获取相似商品
$similarProducts = getProducts(4, 0, null, null);

// 检查用户是否已收藏该商品
$isFavorited = false;
if (isLoggedIn()) {
    $stmt = $pdo->prepare("SELECT id FROM favorites WHERE user_id = ? AND product_id = ?");
    $stmt->execute([$_SESSION['user_id'], $productId]);
    $isFavorited = $stmt->fetch() !== false;
}
?>

<!-- 主要内容区 -->
<main class="main detail-page">
    <div class="container">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-nav">
            <a href="index.php">首页</a>
            <i class="bi bi-chevron-right"></i>
            <a href="list.php?category=<?php echo $product['category_name']; ?>"><?php echo htmlspecialchars($product['category_name']); ?></a>
            <i class="bi bi-chevron-right"></i>
            <span class="current"><?php echo htmlspecialchars(mb_substr($product['title'], 0, 30)); ?>...</span>
        </div>

        <!-- 商品详情主体 -->
        <div class="product-detail-container">
            <!-- 左侧：商品图片区域 -->
            <div class="product-gallery-section">
                <div class="gallery-main-container">
                    <div class="gallery-main-image">
                        <img src="<?php echo $images[0]; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>" id="mainProductImage">
                        <div class="image-zoom-overlay">
                            <i class="bi bi-zoom-in"></i>
                            <span>点击查看大图</span>
                        </div>
                    </div>
                </div>

                <!-- 缩略图列表 -->
                <div class="gallery-thumbnails">
                    <?php foreach ($images as $index => $image): ?>
                        <div class="thumbnail-item <?php echo $index === 0 ? 'active' : ''; ?>" data-image="<?php echo $image; ?>">
                            <img src="<?php echo $image; ?>" alt="商品图片<?php echo $index + 1; ?>">
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- 右侧：商品信息区域 -->
            <div class="product-info-section">
                <!-- 商品标题和价格 -->
                <div class="product-header-info">
                    <h1 class="product-title"><?php echo htmlspecialchars($product['title']); ?></h1>

                    <div class="product-pricing">
                        <div class="current-price">
                            <span class="price-symbol">¥</span>
                            <span class="price-value"><?php echo number_format($product['price'], 2); ?></span>
                        </div>
                        <?php if ($product['original_price'] && $product['original_price'] > $product['price']): ?>
                            <div class="original-price">
                                <span>原价：¥<?php echo number_format($product['original_price'], 2); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- 商品标签 -->
                    <div class="product-tags">
                        <?php if ($product['is_virtual']): ?>
                            <span class="tag virtual-tag">
                                <i class="bi bi-lightning-charge"></i>
                                虚拟商品
                            </span>
                        <?php endif; ?>
                        <span class="tag guarantee-tag">
                            <i class="bi bi-shield-check"></i>
                            担保交易
                        </span>
                        <?php if ($product['delivery_method'] === 'automatic'): ?>
                            <span class="tag auto-delivery-tag">
                                <i class="bi bi-robot"></i>
                                自动发货
                            </span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 商品基本信息 -->
                <div class="product-basic-info">
                    <div class="info-row">
                        <span class="info-label">商品编号</span>
                        <span class="info-value"><?php echo $product['id']; ?></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">库存数量</span>
                        <span class="info-value"><?php echo $product['stock']; ?> 件</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">浏览次数</span>
                        <span class="info-value"><?php echo $product['views']; ?> 次</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">发布时间</span>
                        <span class="info-value"><?php echo date('Y-m-d H:i', strtotime($product['created_at'])); ?></span>
                    </div>
                </div>

                <!-- 虚拟商品属性 -->
                <?php if ($product['is_virtual'] && $product['usage_period']): ?>
                    <div class="virtual-product-attrs">
                        <h3 class="attrs-title">商品属性</h3>
                        <div class="attrs-grid">
                            <div class="attr-item">
                                <span class="attr-label">使用期限</span>
                                <span class="attr-value">
                                    <?php
                                    if ($product['usage_period'] === 'unlimited') {
                                        echo '<span class="unlimited-badge">永久有效</span>';
                                    } else {
                                        echo $product['period_value'] . ' ' .
                                             ($product['period_unit'] === 'day' ? '天' :
                                              ($product['period_unit'] === 'month' ? '个月' : '年'));
                                    }
                                    ?>
                                </span>
                            </div>
                            <?php if ($product['platform']): ?>
                                <div class="attr-item">
                                    <span class="attr-label">适用平台</span>
                                    <span class="attr-value"><?php echo htmlspecialchars($product['platform']); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- 购买操作区域 -->
                <div class="purchase-actions">
                    <!-- 库存信息 -->
                    <div class="stock-info-display">
                        <span class="stock-label">库存</span>
                        <span class="stock-number"><?php echo $product['stock']; ?> 件</span>
                    </div>

                    <!-- 主要购买按钮 -->
                    <div class="main-purchase-buttons">
                        <?php if (isLoggedIn() && $_SESSION['user_id'] != $product['user_id']): ?>
                            <button class="btn-buy-now" onclick="buyNow(<?php echo $product['id']; ?>)">
                                <i class="bi bi-lightning-charge"></i>
                                立即购买
                            </button>
                            <button class="btn-add-cart" onclick="addToCart(<?php echo $product['id']; ?>)">
                                <i class="bi bi-cart-plus"></i>
                                加入购物车
                            </button>
                        <?php elseif (!isLoggedIn()): ?>
                            <a href="login.php" class="btn-login">
                                <i class="bi bi-person-check"></i>
                                登录后购买
                            </a>
                        <?php else: ?>
                            <button class="btn-disabled" disabled>
                                <i class="bi bi-person-gear"></i>
                                这是您发布的商品
                            </button>
                        <?php endif; ?>
                    </div>

                    <!-- 次要操作 -->
                    <div class="secondary-purchase-actions">
                        <?php if (isLoggedIn() && $_SESSION['user_id'] != $product['user_id']): ?>
                            <button class="action-item <?php echo $isFavorited ? 'favorited' : ''; ?>" onclick="toggleFavorite(<?php echo $product['id']; ?>)">
                                <i class="bi bi-heart<?php echo $isFavorited ? '-fill' : ''; ?>"></i>
                                <span><?php echo $isFavorited ? '已收藏' : '收藏'; ?></span>
                            </button>
                        <?php endif; ?>
                        <button class="action-item" onclick="shareProduct()">
                            <i class="bi bi-share"></i>
                            <span>分享</span>
                        </button>
                        <button class="action-item" onclick="contactSeller()">
                            <i class="bi bi-chat-dots"></i>
                            <span>咨询</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 卖家信息卡片 -->
            <div class="seller-info-card">
                <div class="seller-header">
                    <img src="<?php echo $product['seller_avatar']; ?>" alt="卖家头像" class="seller-avatar">
                    <div class="seller-details">
                        <h3 class="seller-name"><?php echo htmlspecialchars($product['seller_name']); ?></h3>
                        <div class="seller-rating">
                            <div class="rating-stars">
                                <?php
                                $rating = $product['seller_rating'];
                                for ($i = 1; $i <= 5; $i++) {
                                    if ($i <= $rating) {
                                        echo '<i class="bi bi-star-fill"></i>';
                                    } elseif ($i - 0.5 <= $rating) {
                                        echo '<i class="bi bi-star-half"></i>';
                                    } else {
                                        echo '<i class="bi bi-star"></i>';
                                    }
                                }
                                ?>
                            </div>
                            <span class="rating-score"><?php echo number_format($product['seller_rating'], 1); ?></span>
                        </div>
                    </div>
                </div>

                <div class="seller-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo $product['total_sales'] ?? 0; ?></span>
                        <span class="stat-label">已售出</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <span class="stat-label">好评率</span>
                    </div>
                </div>

                <?php if (isLoggedIn() && $_SESSION['user_id'] != $product['user_id']): ?>
                    <button class="contact-seller-btn" onclick="contactSeller()">
                        <i class="bi bi-chat-dots"></i>
                        联系卖家
                    </button>
                <?php endif; ?>
            </div>
        </div>

        <!-- 商品详细信息区域 -->
        <div class="product-details-section">
            <!-- 选项卡导航 -->
            <div class="detail-tabs">
                <button class="tab-btn active" data-tab="description">商品描述</button>
                <?php if ($product['is_virtual']): ?>
                    <button class="tab-btn" data-tab="attributes">商品属性</button>
                <?php endif; ?>
                <button class="tab-btn" data-tab="purchase-info">购买须知</button>
            </div>

            <!-- 选项卡内容 -->
            <div class="tab-content">
                <!-- 商品描述 -->
                <div class="tab-pane active" id="description">
                    <div class="description-content">
                        <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                    </div>
                </div>

                <!-- 商品属性 -->
                <?php if ($product['is_virtual']): ?>
                    <div class="tab-pane" id="attributes">
                        <div class="attributes-table">
                            <div class="attr-row">
                                <span class="attr-name">商品类型</span>
                                <span class="attr-val">虚拟商品</span>
                            </div>
                            <div class="attr-row">
                                <span class="attr-name">发货方式</span>
                                <span class="attr-val"><?php echo $product['delivery_method'] === 'automatic' ? '自动发货' : '手动发货'; ?></span>
                            </div>
                            <div class="attr-row">
                                <span class="attr-name">使用期限</span>
                                <span class="attr-val">
                                    <?php
                                    if ($product['usage_period'] === 'unlimited') {
                                        echo '永久有效';
                                    } else {
                                        echo $product['period_value'] . ' ' .
                                             ($product['period_unit'] === 'day' ? '天' :
                                              ($product['period_unit'] === 'month' ? '个月' : '年'));
                                    }
                                    ?>
                                </span>
                            </div>
                            <?php if ($product['platform']): ?>
                                <div class="attr-row">
                                    <span class="attr-name">适用平台</span>
                                    <span class="attr-val"><?php echo htmlspecialchars($product['platform']); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- 购买须知 -->
                <div class="tab-pane" id="purchase-info">
                    <div class="purchase-info-content">
                        <div class="info-section">
                            <h4><i class="bi bi-shield-check"></i> 交易保障</h4>
                            <ul>
                                <li>平台担保交易，资金安全有保障</li>
                                <li>支持7天无理由退换货（虚拟商品除外）</li>
                                <li>商品质量问题，平台先行赔付</li>
                            </ul>
                        </div>

                        <?php if ($product['is_virtual']): ?>
                            <div class="info-section">
                                <h4><i class="bi bi-download"></i> 虚拟商品说明</h4>
                                <ul>
                                    <li>购买成功后，系统将自动发送商品内容到您的账户</li>
                                    <li>请确保您的账户信息准确，以便正常接收商品</li>
                                    <li>虚拟商品一经发货，不支持退换货</li>
                                    <li>如有使用问题，请及时联系卖家获得技术支持</li>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <div class="info-section">
                            <h4><i class="bi bi-headset"></i> 售后服务</h4>
                            <ul>
                                <li>商品问题可联系卖家或平台客服</li>
                                <li>客服工作时间：9:00-18:00</li>
                                <li>紧急问题可通过在线客服获得即时帮助</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 相似商品推荐 -->
        <div class="similar-products-section">
            <h3 class="section-title">
                <i class="bi bi-grid"></i>
                相似商品推荐
            </h3>
            <div class="similar-products-grid">
                <?php foreach ($similarProducts as $similar): ?>
                    <div class="similar-product-card">
                        <a href="detail.php?id=<?php echo $similar['id']; ?>">
                            <div class="similar-product-image">
                                <?php
                                $similarImages = json_decode($similar['images'], true);
                                $similarImage = $similarImages ? (UPLOAD_PATH . $similarImages[0]) : 'images/product-default.svg';
                                ?>
                                <img src="<?php echo $similarImage; ?>" alt="<?php echo htmlspecialchars($similar['title']); ?>">
                            </div>
                            <div class="similar-product-content">
                                <h4 class="similar-product-title"><?php echo htmlspecialchars(mb_substr($similar['title'], 0, 40)); ?></h4>
                                <div class="similar-product-price"><?php echo formatPrice($similar['price']); ?></div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</main>

<!-- 购买确认模态框 -->
<div id="purchaseModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>确认购买</h3>
            <span class="close" onclick="closePurchaseModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="purchase-product-info">
                <img id="purchaseProductImage" src="" alt="商品图片" class="purchase-product-img">
                <div class="purchase-product-details">
                    <h4 id="purchaseProductTitle"></h4>
                    <div class="purchase-product-price" id="purchaseProductPrice"></div>
                    <?php if ($product['is_virtual']): ?>
                        <div class="purchase-virtual-info">
                            <div class="virtual-info-item">
                                <i class="bi bi-lightning-charge"></i>
                                <span>虚拟商品，购买后即时发货</span>
                            </div>
                            <?php if ($product['usage_period'] !== 'unlimited'): ?>
                                <div class="virtual-info-item">
                                    <i class="bi bi-clock"></i>
                                    <span>有效期：<?php echo $product['period_value'] . ' ' .
                                        ($product['period_unit'] === 'day' ? '天' :
                                         ($product['period_unit'] === 'month' ? '个月' : '年')); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="purchase-quantity">
                <label>购买数量：</label>
                <div class="quantity-controls">
                    <button type="button" onclick="decreaseQuantity()">-</button>
                    <input type="number" id="purchaseQuantity" value="1" min="1" max="<?php echo $product['stock']; ?>">
                    <button type="button" onclick="increaseQuantity()">+</button>
                </div>
                <span class="stock-info">库存：<?php echo $product['stock']; ?> 件</span>
            </div>
            <div class="purchase-total">
                <span class="total-label">总价：</span>
                <span class="total-price" id="totalPrice"><?php echo formatPrice($product['price']); ?></span>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closePurchaseModal()">取消</button>
            <button class="btn btn-primary" onclick="confirmPurchase()">确认购买</button>
        </div>
    </div>
</div>

<script>
function buyNow(productId) {
    showPurchaseModal(productId, 1); // 默认数量为1
}

function addToCart(productId) {
    const quantity = 1; // 默认数量为1

    const formData = new FormData();
    formData.append('action', 'add');
    formData.append('product_id', productId);
    formData.append('quantity', quantity);

    fetch('api/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('已加入购物车', 'success');
            updateCartCount();
        } else {
            showMessage(data.message || '加入购物车失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

function showPurchaseModal(productId, quantity = 1) {
    // 设置商品信息
    document.getElementById('purchaseProductImage').src = '<?php echo $images[0]; ?>';
    document.getElementById('purchaseProductTitle').textContent = '<?php echo htmlspecialchars($product['title']); ?>';
    document.getElementById('purchaseProductPrice').textContent = '<?php echo formatPrice($product['price']); ?>';

    // 设置数量
    document.getElementById('purchaseQuantity').value = quantity;
    updateTotalPrice();

    // 显示模态框
    document.getElementById('purchaseModal').style.display = 'block';
}

function closePurchaseModal() {
    document.getElementById('purchaseModal').style.display = 'none';
}

function increaseQuantity() {
    const quantityInput = document.getElementById('purchaseQuantity');
    const maxStock = parseInt(quantityInput.getAttribute('max'));
    const currentValue = parseInt(quantityInput.value);

    if (currentValue < maxStock) {
        quantityInput.value = currentValue + 1;
        updateTotalPrice();
    }
}

function decreaseQuantity() {
    const quantityInput = document.getElementById('purchaseQuantity');
    const currentValue = parseInt(quantityInput.value);

    if (currentValue > 1) {
        quantityInput.value = currentValue - 1;
        updateTotalPrice();
    }
}

function updateTotalPrice() {
    const quantity = parseInt(document.getElementById('purchaseQuantity').value);
    const unitPrice = <?php echo $product['price']; ?>;
    const totalPrice = quantity * unitPrice;

    document.getElementById('totalPrice').textContent = '¥' + totalPrice.toFixed(2);
}

function confirmPurchase() {
    const quantity = parseInt(document.getElementById('purchaseQuantity').value);
    const productId = <?php echo $product['id']; ?>;

    // 发送购买请求
    const formData = new FormData();
    formData.append('product_id', productId);
    formData.append('quantity', quantity);
    formData.append('action', 'buy_now');

    fetch('api/purchase.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('购买成功！', 'success');
            closePurchaseModal();
            // 可以跳转到订单页面
            setTimeout(() => {
                window.location.href = 'my-orders.php';
            }, 1500);
        } else {
            showMessage(data.message || '购买失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

function toggleFavorite(productId) {
    // 实现收藏功能
    fetch('api/favorite.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({product_id: productId})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // 更新收藏按钮状态
            const favoriteBtn = document.querySelector('button[onclick="toggleFavorite(' + productId + ')"]');
            if (data.action === 'added') {
                favoriteBtn.innerHTML = '<i class="bi bi-heart-fill"></i> 已收藏';
                favoriteBtn.classList.add('favorited');
            } else {
                favoriteBtn.innerHTML = '<i class="bi bi-heart"></i> 收藏';
                favoriteBtn.classList.remove('favorited');
            }
        } else {
            showMessage(data.message || '操作失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

function shareProduct() {
    // 实现分享功能
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($product['title']); ?>',
            url: window.location.href
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
            showMessage('链接已复制到剪贴板', 'success');
        }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = window.location.href;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showMessage('链接已复制到剪贴板', 'success');
        });
    }
}

function contactSeller() {
    // 实现联系卖家功能
    <?php if (isLoggedIn()): ?>
        // 这里可以打开聊天窗口或跳转到消息页面
        showMessage('联系卖家功能开发中...', 'info');
    <?php else: ?>
        showMessage('请先登录后再联系卖家', 'error');
        setTimeout(() => {
            window.location.href = 'login.php';
        }, 1500);
    <?php endif; ?>
}

function showMessage(message, type = 'info') {
    // 创建消息提示
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-toast message-${type}`;
    messageDiv.textContent = message;

    // 添加到页面
    document.body.appendChild(messageDiv);

    // 显示动画
    setTimeout(() => {
        messageDiv.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        messageDiv.classList.remove('show');
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

function updateCartCount() {
    // 更新购物车数量显示
    const formData = new FormData();
    formData.append('action', 'count');

    fetch('api/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const cartCountElement = document.querySelector('.cart-count');
            if (cartCountElement) {
                cartCountElement.textContent = data.count;
                cartCountElement.style.display = data.count > 0 ? 'inline' : 'none';
            }
        }
    })
    .catch(error => {
        console.error('Error updating cart count:', error);
    });
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 图片缩略图点击事件
    document.querySelectorAll('.thumbnail-item').forEach(thumb => {
        thumb.addEventListener('click', function() {
            document.querySelectorAll('.thumbnail-item').forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            const imageSrc = this.getAttribute('data-image');
            document.getElementById('mainProductImage').src = imageSrc;
        });
    });

    // 主图点击放大功能
    document.querySelector('.gallery-main-image').addEventListener('click', function() {
        const img = this.querySelector('img');
        showImagePreview(img.src);
    });

    // 选项卡功能
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');

            // 移除所有活跃状态
            document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));

            // 添加活跃状态
            this.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // 移除了数量控制器，默认购买数量为1

    // 模态框点击外部关闭
    const modal = document.getElementById('purchaseModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closePurchaseModal();
            }
        });
    }

    // 数量输入框变化事件
    const purchaseQuantity = document.getElementById('purchaseQuantity');
    if (purchaseQuantity) {
        purchaseQuantity.addEventListener('input', updateTotalPrice);
    }

    // 初始化购物车数量
    updateCartCount();
});

// 数量控制函数（仅用于模态框）
function increaseQuantity() {
    const quantityInput = document.getElementById('purchaseQuantity');
    const maxStock = parseInt(quantityInput.getAttribute('max'));
    const currentValue = parseInt(quantityInput.value);

    if (currentValue < maxStock) {
        quantityInput.value = currentValue + 1;
        updateTotalPrice();
    }
}

function decreaseQuantity() {
    const quantityInput = document.getElementById('purchaseQuantity');
    const currentValue = parseInt(quantityInput.value);

    if (currentValue > 1) {
        quantityInput.value = currentValue - 1;
        updateTotalPrice();
    }
}

// 图片预览功能
function showImagePreview(imageSrc) {
    const overlay = document.createElement('div');
    overlay.className = 'image-preview-overlay';
    overlay.innerHTML = `
        <div class="image-preview-container">
            <img src="${imageSrc}" alt="商品图片预览" class="preview-image">
            <button class="preview-close" onclick="closeImagePreview()">&times;</button>
        </div>
    `;

    document.body.appendChild(overlay);
    document.body.style.overflow = 'hidden';

    // 点击覆盖层关闭
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            closeImagePreview();
        }
    });

    // ESC键关闭
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeImagePreview();
        }
    });
}

function closeImagePreview() {
    const overlay = document.querySelector('.image-preview-overlay');
    if (overlay) {
        overlay.remove();
        document.body.style.overflow = '';
    }
}
</script>

<style>
/* 重新设计的商品详情页样式 */

/* 全局样式重置 */
.detail-page {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

.detail-page .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 面包屑导航 */
.breadcrumb-nav {
    background: white;
    padding: 15px 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.breadcrumb-nav a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-nav a:hover {
    color: #ff6b35;
}

.breadcrumb-nav .current {
    color: #333;
    font-weight: 500;
}

.breadcrumb-nav i {
    color: #ccc;
    font-size: 12px;
}

/* 主要布局容器 */
.product-detail-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    margin-bottom: 30px;
}

/* 左侧：商品图片区域 */
.product-gallery-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.gallery-main-container {
    margin-bottom: 20px;
}

.gallery-main-image {
    position: relative;
    width: 100%;
    height: 500px;
    border-radius: 12px;
    overflow: hidden;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gallery-main-image:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.gallery-main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.gallery-main-image:hover img {
    transform: scale(1.03);
}

.image-zoom-overlay {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.75);
    color: white;
    padding: 10px 15px;
    border-radius: 25px;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 6px;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(10px);
}

.gallery-main-image:hover .image-zoom-overlay {
    opacity: 1;
}

/* 缩略图优化 */
.gallery-thumbnails {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding: 10px 0;
    scrollbar-width: thin;
    scrollbar-color: #ddd transparent;
}

.gallery-thumbnails::-webkit-scrollbar {
    height: 6px;
}

.gallery-thumbnails::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.gallery-thumbnails::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
}

.thumbnail-item {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.thumbnail-item:hover {
    border-color: #ff6b35;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(255, 107, 53, 0.3);
}

.thumbnail-item.active {
    border-color: #ff6b35;
    box-shadow: 0 6px 15px rgba(255, 107, 53, 0.4);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.thumbnail-item:hover img {
    transform: scale(1.1);
}

/* 右侧：商品信息区域 */
.product-info-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    height: fit-content;
    position: sticky;
    top: 20px;
}

/* 商品标题和价格 */
.product-header-info {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.product-title {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    line-height: 1.4;
    margin-bottom: 15px;
}

.product-pricing {
    margin-bottom: 15px;
}

.current-price {
    display: flex;
    align-items: baseline;
    gap: 2px;
    margin-bottom: 8px;
}

.price-symbol {
    font-size: 20px;
    color: #ff6b35;
    font-weight: 600;
}

.price-value {
    font-size: 32px;
    font-weight: 700;
    color: #ff6b35;
}

.original-price {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

/* 商品标签 */
.product-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.virtual-tag {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1976d2;
}

.guarantee-tag {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #388e3c;
}

.auto-delivery-tag {
    background: linear-gradient(135deg, #fff3e0, #ffcc02);
    color: #f57c00;
}

/* 商品基本信息 */
.product-basic-info {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    font-size: 14px;
}

.info-label {
    color: #666;
    font-weight: 500;
}

.info-value {
    color: #333;
    font-weight: 600;
}

/* 虚拟商品属性 */
.virtual-product-attrs {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.attrs-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.attrs-grid {
    display: grid;
    gap: 10px;
}

.attr-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 14px;
}

.attr-label {
    color: #666;
    font-weight: 500;
}

.attr-value {
    color: #333;
    font-weight: 600;
}

.unlimited-badge {
    background: linear-gradient(135deg, #4caf50, #66bb6a);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* 购买操作区域 */
.purchase-actions {
    margin-bottom: 25px;
}

/* 库存信息显示 */
.stock-info-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #ff6b35;
}

.stock-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.stock-number {
    font-size: 16px;
    color: #333;
    font-weight: 700;
}

/* 主要购买按钮 */
.main-purchase-buttons {
    display: grid;
    gap: 12px;
    margin-bottom: 20px;
}

.btn-buy-now,
.btn-add-cart,
.btn-login,
.btn-disabled {
    width: 100%;
    padding: 16px 20px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
    min-height: 50px;
}

.btn-buy-now {
    background: linear-gradient(135deg, #ff6b35, #ff5722);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.btn-buy-now:hover {
    background: linear-gradient(135deg, #ff5722, #e64a19);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
}

.btn-add-cart {
    background: white;
    color: #ff6b35;
    border: 2px solid #ff6b35;
}

.btn-add-cart:hover {
    background: #ff6b35;
    color: white;
    transform: translateY(-2px);
}

.btn-login {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    color: white;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.btn-login:hover {
    background: linear-gradient(135deg, #1976d2, #1565c0);
    transform: translateY(-2px);
}

.btn-disabled {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
}

/* 次要购买操作 */
.secondary-purchase-actions {
    display: flex;
    justify-content: space-around;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 10px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 12px;
    min-width: 60px;
}

.action-item:hover {
    background: #f8f9fa;
    color: #ff6b35;
    transform: translateY(-2px);
}

.action-item.favorited {
    color: #ff6b35;
}

.action-item i {
    font-size: 18px;
}

/* 卖家信息卡片 */
.seller-info-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-top: 20px;
}

.seller-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.seller-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #f0f0f0;
}

.seller-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.seller-rating {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.rating-stars i {
    color: #ffc107;
    font-size: 14px;
}

.rating-score {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.seller-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    padding: 15px 0;
    background: #f8f9fa;
    border-radius: 12px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #ff6b35;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.contact-seller-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #ff6b35, #ff5722);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.contact-seller-btn:hover {
    background: linear-gradient(135deg, #ff5722, #e64a19);
    transform: translateY(-2px);
}

/* 商品详细信息区域 */
.product-details-section {
    background: white;
    border-radius: 16px;
    padding: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    overflow: hidden;
}

.detail-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 20px;
    background: none;
    border: none;
    font-size: 16px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.tab-btn:hover {
    color: #ff6b35;
    background: rgba(255, 107, 53, 0.05);
}

.tab-btn.active {
    color: #ff6b35;
    background: white;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #ff6b35, #ff5722);
}

.tab-content {
    padding: 30px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.description-content {
    font-size: 15px;
    line-height: 1.8;
    color: #333;
}

.attributes-table {
    display: grid;
    gap: 15px;
}

.attr-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #ff6b35;
}

.attr-name {
    font-weight: 600;
    color: #333;
}

.attr-val {
    color: #666;
    font-weight: 500;
}

.purchase-info-content {
    display: grid;
    gap: 25px;
}

.info-section h4 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.info-section h4 i {
    color: #ff6b35;
    font-size: 18px;
}

.info-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-section li {
    padding: 8px 0;
    padding-left: 20px;
    position: relative;
    color: #666;
    line-height: 1.6;
}

.info-section li::before {
    content: '•';
    color: #ff6b35;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* 相似商品推荐 */
.similar-products-section {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ff6b35;
}

.section-title i {
    color: #ff6b35;
    font-size: 22px;
}

.similar-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.similar-product-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    background: white;
}

.similar-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.similar-product-image {
    height: 150px;
    overflow: hidden;
}

.similar-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.similar-product-card:hover .similar-product-image img {
    transform: scale(1.1);
}

.similar-product-content {
    padding: 15px;
}

.similar-product-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
    height: 40px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.similar-product-price {
    font-size: 16px;
    font-weight: 700;
    color: #ff6b35;
}

/* 图片预览样式 */
.image-preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
}

.image-preview-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.preview-close {
    position: absolute;
    top: -40px;
    right: -40px;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.preview-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 购买模态框样式 */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
    transition: color 0.2s;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 24px;
}

.purchase-product-info {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.purchase-product-img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #eee;
}

.purchase-product-details h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}

.purchase-product-price {
    font-size: 18px;
    font-weight: 700;
    color: #ff6b35;
    margin-bottom: 12px;
}

.purchase-virtual-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.virtual-info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #666;
}

.virtual-info-item i {
    color: #4CAF50;
}

.purchase-quantity {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
}

.quantity-controls button {
    background: #f8f9fa;
    border: none;
    width: 32px;
    height: 32px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
}

.quantity-controls button:hover {
    background: #e9ecef;
}

.quantity-controls input {
    border: none;
    width: 60px;
    height: 32px;
    text-align: center;
    font-size: 14px;
}

.stock-info {
    font-size: 13px;
    color: #666;
}

.purchase-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
    font-size: 16px;
}

.total-price {
    font-size: 20px;
    font-weight: 700;
    color: #ff6b35;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.message-toast.show {
    transform: translateX(0);
}

.message-success {
    background-color: #4CAF50;
}

.message-error {
    background-color: #f44336;
}

.message-info {
    background-color: #2196F3;
}

/* 收藏按钮状态 */
.btn.favorited {
    background-color: #ff6b35;
    color: white;
    border-color: #ff6b35;
}

.btn.favorited:hover {
    background-color: #e55a2b;
    border-color: #e55a2b;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .product-detail-container {
        grid-template-columns: 1fr 350px;
        gap: 20px;
    }

    .similar-products-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

@media (max-width: 992px) {
    .product-detail-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .product-info-section {
        position: static;
    }

    .gallery-main-image {
        height: 400px;
    }

    .thumbnail-item {
        width: 70px;
        height: 70px;
    }

    .similar-products-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }
}

@media (max-width: 768px) {
    .detail-page {
        padding: 15px 0;
    }

    .detail-page .container {
        padding: 0 15px;
    }

    .breadcrumb-nav {
        padding: 12px 15px;
        font-size: 13px;
        margin-bottom: 15px;
    }

    .product-gallery-section,
    .product-info-section,
    .product-details-section,
    .similar-products-section {
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 15px;
    }

    .gallery-main-image {
        height: 300px;
    }

    .thumbnail-item {
        width: 60px;
        height: 60px;
    }

    .product-title {
        font-size: 20px;
        line-height: 1.3;
    }

    .price-value {
        font-size: 28px;
    }

    .product-tags {
        gap: 6px;
    }

    .tag {
        padding: 5px 10px;
        font-size: 11px;
    }

    .product-basic-info {
        margin-bottom: 20px;
    }

    .info-row {
        padding: 6px 0;
        font-size: 13px;
    }

    /* 购买操作优化 */
    .stock-info-display {
        padding: 10px 12px;
        margin-bottom: 15px;
    }

    .main-purchase-buttons {
        gap: 10px;
        margin-bottom: 15px;
    }

    .btn-buy-now,
    .btn-add-cart,
    .btn-login,
    .btn-disabled {
        padding: 14px 18px;
        font-size: 15px;
        min-height: 48px;
    }

    .secondary-purchase-actions {
        padding-top: 12px;
    }

    .action-item {
        padding: 8px;
        font-size: 11px;
        min-width: 50px;
    }

    .action-item i {
        font-size: 16px;
    }

    /* 卖家信息卡片 */
    .seller-info-card {
        padding: 18px;
    }

    .seller-header {
        gap: 12px;
        margin-bottom: 15px;
    }

    .seller-avatar {
        width: 50px;
        height: 50px;
    }

    .seller-name {
        font-size: 16px;
    }

    .seller-stats {
        padding: 12px 0;
        margin-bottom: 15px;
    }

    .contact-seller-btn {
        padding: 10px;
        font-size: 13px;
    }

    /* 选项卡优化 */
    .tab-btn {
        padding: 15px 8px;
        font-size: 14px;
    }

    .tab-content {
        padding: 18px;
    }

    .similar-products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    /* 模态框优化 */
    .modal-content {
        margin: 5% auto;
        width: 95%;
        max-width: 500px;
    }

    .purchase-product-info {
        flex-direction: column;
        text-align: center;
    }

    .purchase-quantity {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .modal-footer {
        flex-direction: column;
        gap: 10px;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .detail-page {
        padding: 10px 0;
        background: #f5f5f5;
    }

    .detail-page .container {
        padding: 0 10px;
    }

    .breadcrumb-nav {
        padding: 8px 12px;
        font-size: 12px;
        margin-bottom: 10px;
        border-radius: 8px;
    }

    .breadcrumb-nav .current {
        display: none; /* 隐藏过长的标题 */
    }

    .product-gallery-section,
    .product-info-section,
    .product-details-section,
    .similar-products-section {
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 10px;
    }

    /* 图片区域优化 */
    .gallery-main-image {
        height: 250px;
        border-radius: 8px;
    }

    .gallery-thumbnails {
        gap: 8px;
        padding: 8px 0;
    }

    .thumbnail-item {
        width: 50px;
        height: 50px;
        border-radius: 6px;
    }

    /* 商品信息区域 */
    .product-header-info {
        margin-bottom: 20px;
        padding-bottom: 15px;
    }

    .product-title {
        font-size: 18px;
        line-height: 1.3;
        margin-bottom: 12px;
    }

    .current-price {
        margin-bottom: 6px;
    }

    .price-symbol {
        font-size: 16px;
    }

    .price-value {
        font-size: 24px;
    }

    .product-tags {
        gap: 4px;
    }

    .tag {
        padding: 3px 6px;
        font-size: 10px;
        border-radius: 12px;
    }

    /* 基本信息优化 */
    .product-basic-info {
        margin-bottom: 15px;
        padding-bottom: 15px;
    }

    .info-row {
        padding: 5px 0;
        font-size: 12px;
    }

    /* 购买操作大幅优化 */
    .purchase-actions {
        margin-bottom: 20px;
    }

    .stock-info-display {
        padding: 8px 12px;
        margin-bottom: 12px;
        font-size: 13px;
    }

    .stock-label {
        font-size: 12px;
    }

    .stock-number {
        font-size: 14px;
    }

    .main-purchase-buttons {
        gap: 8px;
        margin-bottom: 12px;
    }

    .btn-buy-now,
    .btn-add-cart,
    .btn-login,
    .btn-disabled {
        padding: 12px 16px;
        font-size: 14px;
        min-height: 44px;
        border-radius: 8px;
    }

    .btn-buy-now i,
    .btn-add-cart i,
    .btn-login i {
        font-size: 14px;
    }

    .secondary-purchase-actions {
        padding-top: 10px;
        gap: 5px;
    }

    .action-item {
        padding: 6px 4px;
        font-size: 10px;
        min-width: 45px;
        gap: 3px;
    }

    .action-item i {
        font-size: 14px;
    }

    /* 卖家信息卡片 */
    .seller-info-card {
        padding: 15px;
    }

    .seller-header {
        gap: 10px;
        margin-bottom: 12px;
    }

    .seller-avatar {
        width: 45px;
        height: 45px;
    }

    .seller-name {
        font-size: 15px;
        margin-bottom: 6px;
    }

    .rating-stars i {
        font-size: 12px;
    }

    .rating-score {
        font-size: 12px;
    }

    .seller-stats {
        padding: 10px 0;
        margin-bottom: 12px;
    }

    .stat-number {
        font-size: 16px;
        margin-bottom: 2px;
    }

    .stat-label {
        font-size: 11px;
    }

    .contact-seller-btn {
        padding: 8px;
        font-size: 12px;
        border-radius: 6px;
    }

    /* 选项卡优化 */
    .tab-btn {
        padding: 10px 6px;
        font-size: 12px;
    }

    .tab-content {
        padding: 12px;
    }

    .description-content {
        font-size: 14px;
        line-height: 1.6;
    }

    .attr-row {
        padding: 10px 12px;
        font-size: 12px;
    }

    .info-section h4 {
        font-size: 14px;
        margin-bottom: 10px;
    }

    .info-section li {
        font-size: 12px;
        padding: 6px 0;
        padding-left: 15px;
    }

    /* 相似商品 */
    .section-title {
        font-size: 16px;
        margin-bottom: 15px;
        padding-bottom: 10px;
    }

    .similar-products-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .similar-product-image {
        height: 120px;
    }

    .similar-product-content {
        padding: 10px;
    }

    .similar-product-title {
        font-size: 12px;
        height: 32px;
        line-height: 1.3;
    }

    .similar-product-price {
        font-size: 13px;
    }

    /* 模态框移动端优化 */
    .modal-content {
        margin: 2% auto;
        width: 98%;
        max-width: none;
        border-radius: 10px;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-header h3 {
        font-size: 16px;
    }

    .modal-body {
        padding: 15px;
    }

    .modal-footer {
        padding: 15px;
        gap: 8px;
    }

    .modal-footer .btn {
        padding: 10px;
        font-size: 14px;
    }
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.message-toast.show {
    transform: translateX(0);
}

.message-success {
    background: linear-gradient(135deg, #4caf50, #45a049);
}

.message-error {
    background: linear-gradient(135deg, #f44336, #d32f2f);
}

.message-info {
    background: linear-gradient(135deg, #2196f3, #1976d2);
}

/* 移动端消息提示优化 */
@media (max-width: 576px) {
    .message-toast {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        padding: 12px 15px;
        font-size: 14px;
        transform: translateY(-100%);
    }

    .message-toast.show {
        transform: translateY(0);
    }

    /* 增加点击区域，确保触摸友好 */
    .thumbnail-item,
    .action-item,
    .tab-btn {
        min-height: 44px;
        min-width: 44px;
    }

    /* 防止文本选择和点击高亮 */
    .btn-buy-now,
    .btn-add-cart,
    .btn-login,
    .action-item,
    .tab-btn {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: transparent;
    }

    /* 优化滚动体验 */
    .gallery-thumbnails {
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
    }

    .gallery-thumbnails::-webkit-scrollbar {
        display: none;
    }

    /* 确保图片响应式 */
    .gallery-main-image img,
    .thumbnail-item img,
    .similar-product-image img {
        max-width: 100%;
        height: auto;
    }

    /* 优化文本显示 */
    body {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
    }

    /* 确保按钮在移动端有足够的间距 */
    .main-purchase-buttons .btn-buy-now,
    .main-purchase-buttons .btn-add-cart,
    .main-purchase-buttons .btn-login {
        margin-bottom: 2px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
