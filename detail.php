<?php
// 先处理逻辑，再包含header
require_once 'includes/functions.php';

$productId = intval($_GET['id'] ?? 0);

if (!$productId) {
    redirect('index.php');
}

// 获取商品详情
$product = getProduct($productId);

if (!$product) {
    $_SESSION['error_message'] = '商品不存在';
    redirect('index.php');
}

// 增加浏览次数
incrementViews($productId);

// 记录浏览历史（使用新的函数）
addBrowseHistory($productId);

$pageTitle = htmlspecialchars($product['title']);
$images = json_decode($product['images'], true);
if ($images) {
    // 为图片添加路径前缀
    $images = array_map(function($img) {
        return UPLOAD_PATH . $img;
    }, $images);
} else {
    $images = ['images/product-default.svg'];
}

require_once 'includes/header.php';

// 获取相似商品
$similarProducts = getProducts(4, 0, null, null);

// 检查用户是否已收藏该商品
$isFavorited = false;
if (isLoggedIn()) {
    $stmt = $pdo->prepare("SELECT id FROM favorites WHERE user_id = ? AND product_id = ?");
    $stmt->execute([$_SESSION['user_id'], $productId]);
    $isFavorited = $stmt->fetch() !== false;
}
?>

<!-- 主要内容区 -->
<main class="main detail-page">
    <div class="container">
        <!-- 商品详情 -->
        <div class="product-detail">
            <!-- 面包屑导航 -->
            <div class="breadcrumb">
                <a href="index.php">首页</a> &gt; 
                <a href="list.php?category=<?php echo $product['category_name']; ?>"><?php echo htmlspecialchars($product['category_name']); ?></a> &gt; 
                <span class="current-category"><?php echo htmlspecialchars($product['title']); ?></span>
            </div>

            <!-- 商品画廊 -->
            <div class="product-gallery">
                <div class="gallery-thumbs">
                    <?php foreach ($images as $index => $image): ?>
                        <div class="gallery-thumb <?php echo $index === 0 ? 'active' : ''; ?>">
                            <img src="<?php echo $image; ?>" alt="商品图片<?php echo $index + 1; ?>">
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="gallery-main">
                    <div class="gallery-main-img">
                        <img src="<?php echo $images[0]; ?>" alt="<?php echo htmlspecialchars($product['title']); ?>">
                    </div>
                </div>
            </div>

            <!-- 商品信息 -->
            <div class="product-header">
                <h1><?php echo htmlspecialchars($product['title']); ?></h1>
                <div class="product-price-info">
                    <span class="detail-price"><?php echo formatPrice($product['price']); ?></span>
                    <?php if ($product['original_price']): ?>
                        <span class="detail-original-price"><?php echo formatPrice($product['original_price']); ?></span>
                    <?php endif; ?>
                </div>
                <div class="product-meta-info">
                    <?php if ($product['is_virtual']): ?>
                        <div class="meta-item">
                            <i class="bi bi-lightning-charge"></i>
                            <span>即时交付</span>
                        </div>
                    <?php endif; ?>
                    <div class="meta-item">
                        <i class="bi bi-shield-check"></i>
                        <span>担保交易</span>
                    </div>
                    <div class="meta-item">
                        <i class="bi bi-eye"></i>
                        <span>浏览 <?php echo $product['views']; ?> 次</span>
                    </div>
                    <div class="meta-item">
                        <i class="bi bi-calendar3"></i>
                        <span>发布于 <?php echo date('Y-m-d', strtotime($product['created_at'])); ?></span>
                    </div>
                </div>
            </div>

            <!-- 商品描述 -->
            <div class="product-description">
                <h2 class="description-title">商品描述</h2>
                <div class="description-content">
                    <?php echo nl2br(htmlspecialchars($product['description'])); ?>
                </div>
            </div>

            <!-- 虚拟商品属性 -->
            <?php if ($product['is_virtual'] && $product['usage_period']): ?>
                <div class="product-attributes">
                    <h2 class="description-title">商品属性</h2>
                    <div class="attributes-list">
                        <div class="attribute-item">
                            <div class="attribute-label">商品类型</div>
                            <div class="attribute-value">虚拟商品</div>
                        </div>
                        <div class="attribute-item">
                            <div class="attribute-label">使用期限</div>
                            <div class="attribute-value">
                                <?php 
                                if ($product['usage_period'] === 'unlimited') {
                                    echo '永久有效';
                                } else {
                                    echo $product['period_value'] . ' ' . 
                                         ($product['period_unit'] === 'day' ? '天' : 
                                          ($product['period_unit'] === 'month' ? '个月' : '年'));
                                }
                                ?>
                            </div>
                        </div>
                        <div class="attribute-item">
                            <div class="attribute-label">发货方式</div>
                            <div class="attribute-value">
                                <?php echo $product['delivery_method'] === 'automatic' ? '自动发货' : '手动发货'; ?>
                            </div>
                        </div>
                        <?php if ($product['platform']): ?>
                            <div class="attribute-item">
                                <div class="attribute-label">使用平台</div>
                                <div class="attribute-value"><?php echo htmlspecialchars($product['platform']); ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- 交易按钮 -->
            <div class="trade-actions">
                <?php if (isLoggedIn() && $_SESSION['user_id'] != $product['user_id']): ?>
                    <button class="btn btn-primary" onclick="buyNow(<?php echo $product['id']; ?>)">立即购买</button>
                    <button class="btn btn-outline" onclick="addToCart(<?php echo $product['id']; ?>)">加入购物车</button>
                    <button class="btn btn-outline <?php echo $isFavorited ? 'favorited' : ''; ?>" onclick="toggleFavorite(<?php echo $product['id']; ?>)">
                        <i class="bi bi-heart<?php echo $isFavorited ? '-fill' : ''; ?>"></i>
                        <?php echo $isFavorited ? '已收藏' : '收藏'; ?>
                    </button>
                <?php elseif (!isLoggedIn()): ?>
                    <a href="login.php" class="btn btn-primary">登录后购买</a>
                <?php else: ?>
                    <button class="btn btn-outline" disabled>这是您发布的商品</button>
                <?php endif; ?>
                <button class="btn btn-outline" onclick="shareProduct()">
                    <i class="bi bi-share"></i> 分享
                </button>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 卖家信息 -->
            <div class="seller-card">
                <div class="seller-profile">
                    <img src="<?php echo $product['seller_avatar']; ?>" alt="卖家头像" class="seller-profile-avatar">
                    <div class="seller-profile-info">
                        <h3><?php echo htmlspecialchars($product['seller_name']); ?></h3>
                        <div class="seller-badges">
                            <span class="seller-badge"><i class="bi bi-star"></i> 评分 <?php echo $product['seller_rating']; ?></span>
                        </div>
                    </div>
                </div>
                <div class="seller-stats">
                    <div class="stat-item">
                        <span class="stat-value"><?php echo number_format($product['seller_rating'], 1); ?></span>
                        <span class="stat-label">评分</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $product['total_sales'] ?? 0; ?></span>
                        <span class="stat-label">已卖出</span>
                    </div>
                </div>
                <?php if (isLoggedIn() && $_SESSION['user_id'] != $product['user_id']): ?>
                    <a href="#" class="contact-seller-btn">联系卖家</a>
                <?php endif; ?>
            </div>

            <!-- 相似商品推荐 -->
            <div class="similar-products">
                <h3>相似商品推荐</h3>
                <?php foreach ($similarProducts as $similar): ?>
                    <div class="similar-product-item">
                        <a href="detail.php?id=<?php echo $similar['id']; ?>">
                            <div class="similar-product-img">
                                <?php
                                $similarImages = json_decode($similar['images'], true);
                                $similarImage = $similarImages ? (UPLOAD_PATH . $similarImages[0]) : 'images/product-default.svg';
                                ?>
                                <img src="<?php echo $similarImage; ?>" alt="<?php echo htmlspecialchars($similar['title']); ?>">
                            </div>
                            <div class="similar-product-info">
                                <h4><?php echo htmlspecialchars(mb_substr($similar['title'], 0, 30)); ?></h4>
                                <div class="similar-product-price"><?php echo formatPrice($similar['price']); ?></div>
                            </div>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</main>

<!-- 购买确认模态框 -->
<div id="purchaseModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>确认购买</h3>
            <span class="close" onclick="closePurchaseModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="purchase-product-info">
                <img id="purchaseProductImage" src="" alt="商品图片" class="purchase-product-img">
                <div class="purchase-product-details">
                    <h4 id="purchaseProductTitle"></h4>
                    <div class="purchase-product-price" id="purchaseProductPrice"></div>
                    <?php if ($product['is_virtual']): ?>
                        <div class="purchase-virtual-info">
                            <div class="virtual-info-item">
                                <i class="bi bi-lightning-charge"></i>
                                <span>虚拟商品，购买后即时发货</span>
                            </div>
                            <?php if ($product['usage_period'] !== 'unlimited'): ?>
                                <div class="virtual-info-item">
                                    <i class="bi bi-clock"></i>
                                    <span>有效期：<?php echo $product['period_value'] . ' ' .
                                        ($product['period_unit'] === 'day' ? '天' :
                                         ($product['period_unit'] === 'month' ? '个月' : '年')); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="purchase-quantity">
                <label>购买数量：</label>
                <div class="quantity-controls">
                    <button type="button" onclick="decreaseQuantity()">-</button>
                    <input type="number" id="purchaseQuantity" value="1" min="1" max="<?php echo $product['stock']; ?>">
                    <button type="button" onclick="increaseQuantity()">+</button>
                </div>
                <span class="stock-info">库存：<?php echo $product['stock']; ?> 件</span>
            </div>
            <div class="purchase-total">
                <span class="total-label">总价：</span>
                <span class="total-price" id="totalPrice"><?php echo formatPrice($product['price']); ?></span>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closePurchaseModal()">取消</button>
            <button class="btn btn-primary" onclick="confirmPurchase()">确认购买</button>
        </div>
    </div>
</div>

<script>
function buyNow(productId) {
    // 显示购买确认模态框
    showPurchaseModal(productId);
}

function addToCart(productId) {
    // 实现加入购物车功能
    const formData = new FormData();
    formData.append('action', 'add');
    formData.append('product_id', productId);
    formData.append('quantity', 1);

    fetch('api/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('已加入购物车', 'success');
            updateCartCount();
        } else {
            showMessage(data.message || '加入购物车失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

function showPurchaseModal(productId) {
    // 设置商品信息
    document.getElementById('purchaseProductImage').src = '<?php echo $images[0]; ?>';
    document.getElementById('purchaseProductTitle').textContent = '<?php echo htmlspecialchars($product['title']); ?>';
    document.getElementById('purchaseProductPrice').textContent = '<?php echo formatPrice($product['price']); ?>';

    // 重置数量
    document.getElementById('purchaseQuantity').value = 1;
    updateTotalPrice();

    // 显示模态框
    document.getElementById('purchaseModal').style.display = 'block';
}

function closePurchaseModal() {
    document.getElementById('purchaseModal').style.display = 'none';
}

function increaseQuantity() {
    const quantityInput = document.getElementById('purchaseQuantity');
    const maxStock = parseInt(quantityInput.getAttribute('max'));
    const currentValue = parseInt(quantityInput.value);

    if (currentValue < maxStock) {
        quantityInput.value = currentValue + 1;
        updateTotalPrice();
    }
}

function decreaseQuantity() {
    const quantityInput = document.getElementById('purchaseQuantity');
    const currentValue = parseInt(quantityInput.value);

    if (currentValue > 1) {
        quantityInput.value = currentValue - 1;
        updateTotalPrice();
    }
}

function updateTotalPrice() {
    const quantity = parseInt(document.getElementById('purchaseQuantity').value);
    const unitPrice = <?php echo $product['price']; ?>;
    const totalPrice = quantity * unitPrice;

    document.getElementById('totalPrice').textContent = '¥' + totalPrice.toFixed(2);
}

function confirmPurchase() {
    const quantity = parseInt(document.getElementById('purchaseQuantity').value);
    const productId = <?php echo $product['id']; ?>;

    // 发送购买请求
    const formData = new FormData();
    formData.append('product_id', productId);
    formData.append('quantity', quantity);
    formData.append('action', 'buy_now');

    fetch('api/purchase.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('购买成功！', 'success');
            closePurchaseModal();
            // 可以跳转到订单页面
            setTimeout(() => {
                window.location.href = 'my-orders.php';
            }, 1500);
        } else {
            showMessage(data.message || '购买失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

function toggleFavorite(productId) {
    // 实现收藏功能
    fetch('api/favorite.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({product_id: productId})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // 更新收藏按钮状态
            const favoriteBtn = document.querySelector('button[onclick="toggleFavorite(' + productId + ')"]');
            if (data.action === 'added') {
                favoriteBtn.innerHTML = '<i class="bi bi-heart-fill"></i> 已收藏';
                favoriteBtn.classList.add('favorited');
            } else {
                favoriteBtn.innerHTML = '<i class="bi bi-heart"></i> 收藏';
                favoriteBtn.classList.remove('favorited');
            }
        } else {
            showMessage(data.message || '操作失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('网络错误，请重试', 'error');
    });
}

function shareProduct() {
    // 实现分享功能
    if (navigator.share) {
        navigator.share({
            title: '<?php echo htmlspecialchars($product['title']); ?>',
            url: window.location.href
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
            showMessage('链接已复制到剪贴板', 'success');
        }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = window.location.href;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showMessage('链接已复制到剪贴板', 'success');
        });
    }
}

function showMessage(message, type = 'info') {
    // 创建消息提示
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-toast message-${type}`;
    messageDiv.textContent = message;

    // 添加到页面
    document.body.appendChild(messageDiv);

    // 显示动画
    setTimeout(() => {
        messageDiv.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        messageDiv.classList.remove('show');
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

function updateCartCount() {
    // 更新购物车数量显示
    const formData = new FormData();
    formData.append('action', 'count');

    fetch('api/cart.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const cartCountElement = document.querySelector('.cart-count');
            if (cartCountElement) {
                cartCountElement.textContent = data.count;
                cartCountElement.style.display = data.count > 0 ? 'inline' : 'none';
            }
        }
    })
    .catch(error => {
        console.error('Error updating cart count:', error);
    });
}

// 图片画廊功能
document.addEventListener('DOMContentLoaded', function() {
    // 图片缩略图点击事件
    document.querySelectorAll('.gallery-thumb').forEach(thumb => {
        thumb.addEventListener('click', function() {
            document.querySelectorAll('.gallery-thumb').forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            const img = this.querySelector('img');
            document.querySelector('.gallery-main-img img').src = img.src;
        });
    });

    // 模态框点击外部关闭
    document.getElementById('purchaseModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closePurchaseModal();
        }
    });

    // 数量输入框变化事件
    document.getElementById('purchaseQuantity').addEventListener('input', updateTotalPrice);

    // 初始化购物车数量
    updateCartCount();
});
</script>

<style>
/* 购买模态框样式 */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
    transition: color 0.2s;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 24px;
}

.purchase-product-info {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.purchase-product-img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #eee;
}

.purchase-product-details h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}

.purchase-product-price {
    font-size: 18px;
    font-weight: 700;
    color: #ff6b35;
    margin-bottom: 12px;
}

.purchase-virtual-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.virtual-info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #666;
}

.virtual-info-item i {
    color: #4CAF50;
}

.purchase-quantity {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
}

.quantity-controls button {
    background: #f8f9fa;
    border: none;
    width: 32px;
    height: 32px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
}

.quantity-controls button:hover {
    background: #e9ecef;
}

.quantity-controls input {
    border: none;
    width: 60px;
    height: 32px;
    text-align: center;
    font-size: 14px;
}

.stock-info {
    font-size: 13px;
    color: #666;
}

.purchase-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
    font-size: 16px;
}

.total-price {
    font-size: 20px;
    font-weight: 700;
    color: #ff6b35;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.message-toast.show {
    transform: translateX(0);
}

.message-success {
    background-color: #4CAF50;
}

.message-error {
    background-color: #f44336;
}

.message-info {
    background-color: #2196F3;
}

/* 收藏按钮状态 */
.btn.favorited {
    background-color: #ff6b35;
    color: white;
    border-color: #ff6b35;
}

.btn.favorited:hover {
    background-color: #e55a2b;
    border-color: #e55a2b;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }

    .purchase-product-info {
        flex-direction: column;
        text-align: center;
    }

    .purchase-quantity {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
