<?php
require_once 'includes/functions.php';
$currentUser = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . SITE_NAME : SITE_NAME . ' - 虚拟商品交易平台'; ?></title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/virtual-product.css">
    <link rel="stylesheet" href="css/theme-colors.css">
    <link rel="stylesheet" href="css/enhanced-style.css">

    <?php if (isset($additionalCSS) && is_array($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $cssFile): ?>
            <link rel="stylesheet" href="<?php echo $cssFile; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/fonts.css">
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="index.php">
                    <img src="images/logo.svg" alt="数字鱼logo">
                    <span class="logo-text">数字鱼</span>
                </a>
            </div>
            <div class="search-box">
                <form action="search.php" method="GET">
                    <input type="text" name="q" placeholder="搜索你想要的虚拟商品" value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>">
                    <button type="submit" class="search-btn"><i class="bi bi-search"></i></button>
                </form>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="index.php" <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'class="active"' : ''; ?>>首页</a></li>
                    <li><a href="list.php">同城</a></li>
                    <li><a href="publish.php" class="publish-btn" <?php echo basename($_SERVER['PHP_SELF']) == 'publish.php' ? 'active' : ''; ?>>发布商品</a></li>
                    <li><a href="message.php">消息</a></li>
                    <li><a href="member.php" <?php echo basename($_SERVER['PHP_SELF']) == 'member.php' ? 'class="active"' : ''; ?>>我的</a></li>
                </ul>
            </nav>
            <div class="user-info">
                <?php if ($currentUser): ?>
                    <!-- 消息通知 -->
                    <div class="notification-icon">
                        <a href="messages.php" class="notification-btn">
                            <i class="bi bi-bell"></i>
                            <span class="notification-badge">3</span>
                        </a>
                    </div>

                    <!-- 用户下拉菜单 -->
                    <div class="user-dropdown">
                        <div class="user-trigger">
                            <img src="<?php echo $currentUser['avatar'] ?: 'images/avatar-default.svg'; ?>" alt="用户头像" class="user-avatar-img">
                            <span class="user-name"><?php echo htmlspecialchars($currentUser['nickname']); ?></span>
                            <i class="bi bi-chevron-down dropdown-arrow"></i>
                        </div>
                        <div class="dropdown-menu">
                            <div class="dropdown-header">
                                <img src="<?php echo $currentUser['avatar'] ?: 'images/avatar-default.svg'; ?>" alt="用户头像" class="dropdown-avatar">
                                <div class="dropdown-user-info">
                                    <div class="dropdown-username"><?php echo htmlspecialchars($currentUser['nickname']); ?></div>
                                    <div class="dropdown-user-level">
                                        <i class="bi bi-star-fill"></i>
                                        <span>评分 <?php echo number_format($currentUser['rating'] ?? 5.0, 1); ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a href="member.php" class="dropdown-item">
                                <i class="bi bi-person"></i>
                                <span>个人中心</span>
                            </a>
                            <a href="my-products.php" class="dropdown-item">
                                <i class="bi bi-box-seam"></i>
                                <span>我的发布</span>
                            </a>
                            <a href="my-orders.php" class="dropdown-item">
                                <i class="bi bi-bag"></i>
                                <span>我的订单</span>
                            </a>
                            <a href="my-favorites.php" class="dropdown-item">
                                <i class="bi bi-heart"></i>
                                <span>我的收藏</span>
                            </a>
                            <a href="wallet.php" class="dropdown-item">
                                <i class="bi bi-wallet2"></i>
                                <span>我的钱包</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="settings.php" class="dropdown-item">
                                <i class="bi bi-gear"></i>
                                <span>账号设置</span>
                            </a>
                            <a href="help.php" class="dropdown-item">
                                <i class="bi bi-question-circle"></i>
                                <span>帮助中心</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="logout.php" class="dropdown-item logout-item">
                                <i class="bi bi-box-arrow-right"></i>
                                <span>退出登录</span>
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <a href="login.php" class="login-btn">
                        <i class="bi bi-person-circle"></i>
                        <span>登录/注册</span>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <?php if (isset($_SESSION['success_message'])): ?>
        <div class="alert alert-success">
            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error_message'])): ?>
        <div class="alert alert-error">
            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
        </div>
    <?php endif; ?>

    <script>
    // 用户下拉菜单交互
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userTrigger = document.querySelector('.user-trigger');
        const dropdownMenu = document.querySelector('.dropdown-menu');

        if (userTrigger && dropdownMenu) {
            userTrigger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const isOpen = dropdownMenu.classList.contains('show');

                // 关闭所有其他下拉菜单
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    menu.classList.remove('show');
                });

                // 切换当前下拉菜单
                if (!isOpen) {
                    dropdownMenu.classList.add('show');
                    userTrigger.classList.add('active');
                } else {
                    dropdownMenu.classList.remove('show');
                    userTrigger.classList.remove('active');
                }
            });

            // 点击外部关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!userDropdown.contains(e.target)) {
                    dropdownMenu.classList.remove('show');
                    userTrigger.classList.remove('active');
                }
            });
        }
    });
    </script>
