<?php
// 清理输出缓冲区
if (ob_get_level()) {
    ob_end_clean();
}

require_once '../includes/functions.php';

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '无效的请求方法'], JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取请求参数
$action = $_POST['action'] ?? '';
$productId = intval($_POST['product_id'] ?? 0);
$quantity = intval($_POST['quantity'] ?? 1);

if ($productId <= 0 || $quantity <= 0) {
    echo json_encode(['success' => false, 'message' => '无效的参数'], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 开始事务
    $pdo->beginTransaction();
    
    // 获取商品信息
    $stmt = $pdo->prepare("
        SELECT p.*, u.nickname as seller_name 
        FROM products p 
        JOIN users u ON p.user_id = u.id 
        WHERE p.id = ? AND p.status = 'active'
    ");
    $stmt->execute([$productId]);
    $product = $stmt->fetch();
    
    if (!$product) {
        throw new Exception('商品不存在或已下架');
    }
    
    // 检查是否是自己的商品
    if ($product['user_id'] == $_SESSION['user_id']) {
        throw new Exception('不能购买自己的商品');
    }
    
    // 检查库存
    if ($product['stock'] < $quantity) {
        throw new Exception('库存不足');
    }
    
    // 计算总价
    $totalPrice = $product['price'] * $quantity;
    
    // 创建订单（先不设置订单号，稍后更新）
    $stmt = $pdo->prepare("
        INSERT INTO orders (buyer_id, seller_id, product_id, quantity, total_price, status, created_at)
        VALUES (?, ?, ?, ?, ?, 'pending', NOW())
    ");
    $stmt->execute([
        $_SESSION['user_id'],
        $product['user_id'],
        $productId,
        $quantity,
        $totalPrice
    ]);
    
    $orderId = $pdo->lastInsertId();

    // 生成订单号并更新
    $orderNumber = 'XY' . date('YmdHis') . str_pad($orderId, 4, '0', STR_PAD_LEFT);

    // 尝试更新订单号（如果字段存在）
    try {
        $stmt = $pdo->prepare("UPDATE orders SET order_number = ? WHERE id = ?");
        $stmt->execute([$orderNumber, $orderId]);
    } catch (Exception $e) {
        // 如果字段不存在，忽略错误
        error_log("Order number field not exists: " . $e->getMessage());
    }

    // 减少库存
    $stmt = $pdo->prepare("UPDATE products SET stock = stock - ? WHERE id = ? AND stock >= ?");
    $stmt->execute([$quantity, $productId, $quantity]);

    if ($stmt->rowCount() === 0) {
        throw new Exception('库存不足，请刷新页面重试');
    }
    
    // 如果是虚拟商品且设置为自动发货，直接完成订单
    if ($product['is_virtual']) {
        // 获取虚拟商品属性
        $stmt = $pdo->prepare("SELECT delivery_method FROM virtual_attributes WHERE product_id = ?");
        $stmt->execute([$productId]);
        $virtualAttr = $stmt->fetch();
        
        if ($virtualAttr && $virtualAttr['delivery_method'] === 'automatic') {
            // 自动发货，直接完成订单
            $stmt = $pdo->prepare("UPDATE orders SET status = 'completed' WHERE id = ?");
            $stmt->execute([$orderId]);
            
            // 这里可以添加自动发货逻辑，比如发送虚拟商品内容到用户邮箱或站内信
            // TODO: 实现自动发货功能
        }
    }
    
    // 提交事务
    $pdo->commit();
    
    echo json_encode([
        'success' => true, 
        'message' => '购买成功！',
        'order_id' => $orderId,
        'order_number' => $orderNumber
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // 回滚事务
    $pdo->rollBack();
    
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

exit;
