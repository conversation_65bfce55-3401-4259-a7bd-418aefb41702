/**
 * 闲鱼网站主要JavaScript文件
 * 包含网站所有交互功能
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 初始化轮播图
    initCarousel();
    
    // 初始化商品卡片交互
    initProductCards();
    
    // 初始化搜索功能
    initSearch();
    
    // 初始化筛选和排序
    initFilters();
    
    // 初始化分页
    initPagination();
    
    // 初始化详情页图片预览
    if (document.querySelector('.product-gallery')) {
        initProductGallery();
    }
    
    // 初始化注册表单验证
    if (document.querySelector('.register-form')) {
        initRegisterForm();
    }
    
    // 初始化登录表单验证
    if (document.querySelector('.login-form')) {
        initLoginForm();
    }
    
    // 初始化会员中心功能
    if (document.querySelector('.member-center')) {
        initMemberCenter();
    }
});

/**
 * 初始化轮播图功能
 */
function initCarousel() {
    const carousel = document.querySelector('.carousel');
    if (!carousel) return;
    
    const carouselSlides = carousel.querySelector('.carousel-slides');
    const slides = carousel.querySelectorAll('.carousel-slide');
    const prevBtn = carousel.querySelector('.carousel-prev');
    const nextBtn = carousel.querySelector('.carousel-next');
    const indicators = carousel.querySelectorAll('.carousel-indicator');
    
    let currentSlide = 0;
    const slideCount = slides.length;
    
    // 设置自动轮播间隔（毫秒）
    const autoPlayInterval = 5000;
    let autoPlayTimer;
    
    // 显示指定索引的幻灯片
    function showSlide(index) {
        // 确保索引在有效范围内
        if (index < 0) index = slideCount - 1;
        if (index >= slideCount) index = 0;
        
        // 更新当前幻灯片索引
        currentSlide = index;
        
        // 移动幻灯片容器
        carouselSlides.style.transform = `translateX(-${currentSlide * 100}%)`;
        
        // 更新指示器状态
        indicators.forEach((indicator, i) => {
            indicator.classList.toggle('active', i === currentSlide);
        });
    }
    
    // 显示下一张幻灯片
    function nextSlide() {
        showSlide(currentSlide + 1);
    }
    
    // 显示上一张幻灯片
    function prevSlide() {
        showSlide(currentSlide - 1);
    }
    
    // 设置自动播放
    function startAutoPlay() {
        stopAutoPlay();
        autoPlayTimer = setInterval(nextSlide, autoPlayInterval);
    }
    
    // 停止自动播放
    function stopAutoPlay() {
        if (autoPlayTimer) {
            clearInterval(autoPlayTimer);
        }
    }
    
    // 添加事件监听器
    if (nextBtn) nextBtn.addEventListener('click', () => {
        nextSlide();
        startAutoPlay(); // 用户交互后重置自动播放计时器
    });
    
    if (prevBtn) prevBtn.addEventListener('click', () => {
        prevSlide();
        startAutoPlay(); // 用户交互后重置自动播放计时器
    });
    
    // 为指示器添加点击事件
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            showSlide(index);
            startAutoPlay(); // 用户交互后重置自动播放计时器
        });
    });
    
    // 鼠标悬停时暂停自动播放
    carousel.addEventListener('mouseenter', stopAutoPlay);
    carousel.addEventListener('mouseleave', startAutoPlay);
    
    // 触摸事件处理（移动端）
    let touchStartX = 0;
    let touchEndX = 0;
    
    carousel.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
        stopAutoPlay();
    }, { passive: true });
    
    carousel.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
        startAutoPlay();
    }, { passive: true });
    
    function handleSwipe() {
        const swipeThreshold = 50; // 最小滑动距离
        const diff = touchStartX - touchEndX;
        
        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                // 向左滑动，显示下一张
                nextSlide();
            } else {
                // 向右滑动，显示上一张
                prevSlide();
            }
        }
    }
    
    // 初始化显示第一张幻灯片并启动自动播放
    showSlide(0);
    startAutoPlay();
}

/**
 * 初始化商品卡片交互
 */
function initProductCards() {
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        // 点赞按钮
        const likeBtn = card.querySelector('.like-btn');
        if (likeBtn) {
            likeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // 切换点赞状态
                this.classList.toggle('liked');
                
                // 更新点赞数量
                const likeCount = this.textContent.trim().split(' ')[1];
                let newCount = parseInt(likeCount);
                
                if (this.classList.contains('liked')) {
                    newCount += 1;
                    this.innerHTML = `<i class="bi bi-heart-fill"></i> ${newCount}`;
                } else {
                    newCount -= 1;
                    this.innerHTML = `<i class="bi bi-heart"></i> ${newCount}`;
                }
            });
        }
        
        // 鼠标悬停效果
        card.addEventListener('mouseenter', function() {
            this.classList.add('hover');
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
        });
    });
}

/**
 * 初始化搜索功能
 */
function initSearch() {
    const searchForms = document.querySelectorAll('.search-box');
    
    searchForms.forEach(form => {
        const input = form.querySelector('input');
        const button = form.querySelector('button');
        
        if (input && button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const searchTerm = input.value.trim();
                if (searchTerm) {
                    // 在实际应用中，这里会提交到后端或重定向到搜索结果页
                    window.location.href = `search.html?q=${encodeURIComponent(searchTerm)}`;
                }
            });
            
            // 回车键提交搜索
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    button.click();
                }
            });
        }
    });
}

/**
 * 初始化筛选和排序功能
 */
function initFilters() {
    // 筛选选项点击
    const filterOptions = document.querySelectorAll('.filter-options a');
    filterOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除同组中其他选项的活跃状态
            const parentGroup = this.closest('.filter-options');
            parentGroup.querySelectorAll('a').forEach(opt => {
                opt.classList.remove('active');
            });
            
            // 设置当前选项为活跃
            this.classList.add('active');
            
            // 在实际应用中，这里会触发筛选结果的更新
            // updateProductList();
        });
    });
    
    // 排序选项点击
    const sortOptions = document.querySelectorAll('.sort-options a');
    sortOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除其他排序选项的活跃状态
            sortOptions.forEach(opt => {
                opt.classList.remove('active');
            });
            
            // 设置当前选项为活跃
            this.classList.add('active');
            
            // 在实际应用中，这里会触发排序结果的更新
            // updateProductList();
        });
    });
    
    // 视图切换
    const gridViewBtn = document.querySelector('.grid-view');
    const listViewBtn = document.querySelector('.list-view');
    const productGrid = document.querySelector('.product-grid');
    
    if (gridViewBtn && listViewBtn && productGrid) {
        gridViewBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            gridViewBtn.classList.add('active');
            listViewBtn.classList.remove('active');
            productGrid.classList.remove('list-view-active');
            productGrid.classList.add('grid-view-active');
        });
        
        listViewBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            listViewBtn.classList.add('active');
            gridViewBtn.classList.remove('active');
            productGrid.classList.remove('grid-view-active');
            productGrid.classList.add('list-view-active');
        });
    }
}

/**
 * 初始化分页功能
 */
function initPagination() {
    const pagination = document.querySelector('.pagination');
    if (!pagination) return;
    
    const pageLinks = pagination.querySelectorAll('a:not(.prev-page):not(.next-page)');
    const prevPageBtn = pagination.querySelector('.prev-page');
    const nextPageBtn = pagination.querySelector('.next-page');
    const pageJumpBtn = pagination.querySelector('.page-jump button');
    const pageJumpInput = pagination.querySelector('.page-jump input');
    
    // 页码点击
    pageLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除其他页码的活跃状态
            pageLinks.forEach(l => l.classList.remove('active'));
            
            // 设置当前页码为活跃
            this.classList.add('active');
            
            // 更新页码输入框
            if (pageJumpInput) {
                pageJumpInput.value = this.textContent;
            }
            
            // 在实际应用中，这里会加载对应页面的数据
            // loadPage(parseInt(this.textContent));
        });
    });
    
    // 上一页按钮
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 找到当前活跃页码
            const activePage = pagination.querySelector('a.active:not(.prev-page):not(.next-page)');
            if (activePage && activePage.previousElementSibling && !activePage.previousElementSibling.classList.contains('prev-page')) {
                activePage.previousElementSibling.click();
            }
        });
    }
    
    // 下一页按钮
    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 找到当前活跃页码
            const activePage = pagination.querySelector('a.active:not(.prev-page):not(.next-page)');
            if (activePage && activePage.nextElementSibling && !activePage.nextElementSibling.classList.contains('next-page') && !activePage.nextElementSibling.classList.contains('ellipsis')) {
                activePage.nextElementSibling.click();
            }
        });
    }
    
    // 页码跳转
    if (pageJumpBtn && pageJumpInput) {
        pageJumpBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            const pageNum = parseInt(pageJumpInput.value);
            if (isNaN(pageNum) || pageNum < 1) {
                pageJumpInput.value = '1';
                return;
            }
            
            // 在实际应用中，这里会跳转到指定页码
            // 这里简单模拟点击对应页码链接
            const targetPage = Array.from(pageLinks).find(link => parseInt(link.textContent) === pageNum);
            if (targetPage) {
                targetPage.click();
            } else {
                // 如果目标页码不在当前显示的页码范围内
                // 在实际应用中，这里会直接加载对应页面
                // loadPage(pageNum);
                
                // 更新活跃页码状态
                pageLinks.forEach(l => l.classList.remove('active'));
                
                // 更新页码输入框
                pageJumpInput.value = pageNum;
            }
        });
        
        // 回车键提交页码跳转
        pageJumpInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                pageJumpBtn.click();
            }
        });
    }
}

/**
 * 初始化商品详情页图片预览
 */
function initProductGallery() {
    const gallery = document.querySelector('.product-gallery');
    const mainImage = gallery.querySelector('.main-image img');
    const thumbnails = gallery.querySelectorAll('.thumbnails img');
    
    // 缩略图点击切换主图
    thumbnails.forEach(thumb => {
        thumb.addEventListener('click', function() {
            // 移除其他缩略图的活跃状态
            thumbnails.forEach(t => t.classList.remove('active'));
            
            // 设置当前缩略图为活跃
            this.classList.add('active');
            
            // 更新主图
            mainImage.src = this.src;
            mainImage.alt = this.alt;
        });
    });
    
    // 图片放大预览
    if (mainImage) {
        mainImage.addEventListener('click', function() {
            // 创建全屏预览元素
            const overlay = document.createElement('div');
            overlay.className = 'image-preview-overlay';
            
            const previewImg = document.createElement('img');
            previewImg.src = this.src;
            previewImg.alt = this.alt;
            previewImg.className = 'preview-image';
            
            const closeBtn = document.createElement('button');
            closeBtn.className = 'preview-close';
            closeBtn.innerHTML = '&times;';
            
            overlay.appendChild(previewImg);
            overlay.appendChild(closeBtn);
            document.body.appendChild(overlay);
            
            // 禁止背景滚动
            document.body.style.overflow = 'hidden';
            
            // 关闭预览
            function closePreview() {
                document.body.removeChild(overlay);
                document.body.style.overflow = '';
            }
            
            closeBtn.addEventListener('click', closePreview);
            overlay.addEventListener('click', function(e) {
                if (e.target === overlay) {
                    closePreview();
                }
            });
            
            // ESC键关闭预览
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closePreview();
                }
            }, { once: true });
        });
    }
}

/**
 * 初始化注册表单验证
 */
function initRegisterForm() {
    const registerForm = document.querySelector('.register-form');
    const phoneInput = registerForm.querySelector('input[name="phone"]');
    const codeInput = registerForm.querySelector('input[name="code"]');
    const passwordInput = registerForm.querySelector('input[name="password"]');
    const confirmPasswordInput = registerForm.querySelector('input[name="confirm_password"]');
    const nicknameInput = registerForm.querySelector('input[name="nickname"]');
    const agreementCheckbox = registerForm.querySelector('input[name="agreement"]');
    const submitButton = registerForm.querySelector('button[type="submit"]');
    const getCodeButton = registerForm.querySelector('.get-code-btn');
    
    // 手机号码验证
    phoneInput.addEventListener('input', function() {
        validatePhone(this);
        updateSubmitButtonState();
    });
    
    // 验证码获取
    if (getCodeButton) {
        getCodeButton.addEventListener('click', function(e) {
            e.preventDefault();
            
            if (!validatePhone(phoneInput)) {
                return;
            }
            
            // 模拟发送验证码
            this.disabled = true;
            let countdown = 60;
            this.textContent = `${countdown}秒后重新获取`;
            
            const timer = setInterval(() => {
                countdown--;
                this.textContent = `${countdown}秒后重新获取`;
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    this.disabled = false;
                    this.textContent = '获取验证码';
                }
            }, 1000);
        });
    }
    
    // 密码验证
    passwordInput.addEventListener('input', function() {
        validatePassword(this);
        if (confirmPasswordInput.value) {
            validateConfirmPassword(confirmPasswordInput);
        }
        updateSubmitButtonState();
    });
    
    // 确认密码验证
    confirmPasswordInput.addEventListener('input', function() {
        validateConfirmPassword(this);
        updateSubmitButtonState();
    });
    
    // 昵称验证
    nicknameInput.addEventListener('input', function() {
        validateNickname(this);
        updateSubmitButtonState();
    });
    
    // 协议勾选
    agreementCheckbox.addEventListener('change', function() {
        updateSubmitButtonState();
    });
    
    // 表单提交
    registerForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 验证所有字段
        const isPhoneValid = validatePhone(phoneInput);
        const isPasswordValid = validatePassword(passwordInput);
        const isConfirmPasswordValid = validateConfirmPassword(confirmPasswordInput);
        const isNicknameValid = validateNickname(nicknameInput);
        
        if (isPhoneValid && isPasswordValid && isConfirmPasswordValid && isNicknameValid && agreementCheckbox.checked) {
            // 模拟注册成功
            showMessage('注册成功！正在跳转到登录页面...', 'success');
            
            // 延迟跳转到登录页
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
        } else {
            showMessage('请正确填写所有必填项并同意用户协议', 'error');
        }
    });
    
    // 验证手机号
    function validatePhone(input) {
        const value = input.value.trim();
        const phoneRegex = /^1[3-9]\d{9}$/;
        const isValid = phoneRegex.test(value);
        
        toggleError(input, isValid, '请输入正确的11位手机号码');
        return isValid;
    }
    
    // 验证密码
    function validatePassword(input) {
        const value = input.value.trim();
        const isValid = value.length >= 6;
        
        toggleError(input, isValid, '密码长度至少为6位');
        return isValid;
    }
    
    // 验证确认密码
    function validateConfirmPassword(input) {
        const value = input.value.trim();
        const passwordValue = passwordInput.value.trim();
        const isValid = value === passwordValue && value.length > 0;
        
        toggleError(input, isValid, '两次输入的密码不一致');
        return isValid;
    }
    
    // 验证昵称
    function validateNickname(input) {
        const value = input.value.trim();
        const isValid = value.length >= 2 && value.length <= 20;
        
        toggleError(input, isValid, '昵称长度应为2-20个字符');
        return isValid;
    }
    
    // 切换错误提示
    function toggleError(input, isValid, errorMessage) {
        const formGroup = input.closest('.form-group');
        const errorElement = formGroup.querySelector('.error-message');
        
        if (!isValid) {
            if (!errorElement) {
                const error = document.createElement('div');
                error.className = 'error-message';
                error.textContent = errorMessage;
                formGroup.appendChild(error);
            } else {
                errorElement.textContent = errorMessage;
                errorElement.style.display = 'block';
            }
            input.classList.add('error');
        } else {
            if (errorElement) {
                errorElement.style.display = 'none';
            }
            input.classList.remove('error');
        }
    }
    
    // 更新提交按钮状态
    function updateSubmitButtonState() {
        const isPhoneValid = !phoneInput.classList.contains('error') && phoneInput.value.trim() !== '';
        const isPasswordValid = !passwordInput.classList.contains('error') && passwordInput.value.trim() !== '';
        const isConfirmPasswordValid = !confirmPasswordInput.classList.contains('error') && confirmPasswordInput.value.trim() !== '';
        const isNicknameValid = !nicknameInput.classList.contains('error') && nicknameInput.value.trim() !== '';
        const isAgreementChecked = agreementCheckbox.checked;
        
        submitButton.disabled = !(isPhoneValid && isPasswordValid && isConfirmPasswordValid && isNicknameValid && isAgreementChecked);
    }
}

/**
 * 初始化登录表单验证
 */
function initLoginForm() {
    const loginForm = document.querySelector('.login-form');
    const tabLinks = loginForm.querySelectorAll('.tab-link');
    const tabContents = loginForm.querySelectorAll('.tab-content');
    const phoneLoginTab = loginForm.querySelector('#phone-login');
    const accountLoginTab = loginForm.querySelector('#account-login');
    
    // 切换登录方式
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有标签页的活跃状态
            tabLinks.forEach(tab => tab.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 设置当前标签页为活跃
            this.classList.add('active');
            const target = this.getAttribute('data-target');
            document.querySelector(target).classList.add('active');
        });
    });
    
    // 手机号登录验证
    if (phoneLoginTab) {
        const phoneInput = phoneLoginTab.querySelector('input[name="phone"]');
        const codeInput = phoneLoginTab.querySelector('input[name="code"]');
        const submitButton = phoneLoginTab.querySelector('button[type="submit"]');
        const getCodeButton = phoneLoginTab.querySelector('.get-code-btn');
        
        // 手机号码验证
        phoneInput.addEventListener('input', function() {
            validatePhone(this);
            updatePhoneSubmitButtonState();
        });
        
        // 验证码获取
        if (getCodeButton) {
            getCodeButton.addEventListener('click', function(e) {
                e.preventDefault();
                
                if (!validatePhone(phoneInput)) {
                    return;
                }
                
                // 模拟发送验证码
                this.disabled = true;
                let countdown = 60;
                this.textContent = `${countdown}秒后重新获取`;
                
                const timer = setInterval(() => {
                    countdown--;
                    this.textContent = `${countdown}秒后重新获取`;
                    
                    if (countdown <= 0) {
                        clearInterval(timer);
                        this.disabled = false;
                        this.textContent = '获取验证码';
                    }
                }, 1000);
            });
        }
        
        // 验证码输入验证
        codeInput.addEventListener('input', function() {
            validateCode(this);
            updatePhoneSubmitButtonState();
        });
        
        // 表单提交
        phoneLoginTab.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证所有字段
            const isPhoneValid = validatePhone(phoneInput);
            const isCodeValid = validateCode(codeInput);
            
            if (isPhoneValid && isCodeValid) {
                // 模拟登录成功
                showMessage('登录成功！正在跳转到首页...', 'success');
                
                // 延迟跳转到首页
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);
            } else {
                showMessage('请正确填写手机号和验证码', 'error');
            }
        });
        
        // 验证手机号
        function validatePhone(input) {
            const value = input.value.trim();
            const phoneRegex = /^1[3-9]\d{9}$/;
            const isValid = phoneRegex.test(value);
            
            toggleError(input, isValid, '请输入正确的11位手机号码');
            return isValid;
        }
        
        // 验证验证码
        function validateCode(input) {
            const value = input.value.trim();
            const isValid = /^\d{6}$/.test(value);
            
            toggleError(input, isValid, '请输入6位数字验证码');
            return isValid;
        }
        
        // 更新提交按钮状态
        function updatePhoneSubmitButtonState() {
            const isPhoneValid = !phoneInput.classList.contains('error') && phoneInput.value.trim() !== '';
            const isCodeValid = !codeInput.classList.contains('error') && codeInput.value.trim() !== '';
            
            submitButton.disabled = !(isPhoneValid && isCodeValid);
        }
    }
    
    // 账号密码登录验证
    if (accountLoginTab) {
        const usernameInput = accountLoginTab.querySelector('input[name="username"]');
        const passwordInput = accountLoginTab.querySelector('input[name="password"]');
        const rememberCheckbox = accountLoginTab.querySelector('input[name="remember"]');
        const submitButton = accountLoginTab.querySelector('button[type="submit"]');
        
        // 用户名验证
        usernameInput.addEventListener('input', function() {
            validateUsername(this);
            updateAccountSubmitButtonState();
        });
        
        // 密码验证
        passwordInput.addEventListener('input', function() {
            validatePassword(this);
            updateAccountSubmitButtonState();
        });
        
        // 表单提交
        accountLoginTab.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证所有字段
            const isUsernameValid = validateUsername(usernameInput);
            const isPasswordValid = validatePassword(passwordInput);
            
            if (isUsernameValid && isPasswordValid) {
                // 模拟登录成功
                showMessage('登录成功！正在跳转到首页...', 'success');
                
                // 延迟跳转到首页
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);
            } else {
                showMessage('请正确填写用户名和密码', 'error');
            }
        });
        
        // 验证用户名
        function validateUsername(input) {
            const value = input.value.trim();
            const isValid = value.length >= 3;
            
            toggleError(input, isValid, '用户名长度至少为3位');
            return isValid;
        }
        
        // 验证密码
        function validatePassword(input) {
            const value = input.value.trim();
            const isValid = value.length >= 6;
            
            toggleError(input, isValid, '密码长度至少为6位');
            return isValid;
        }
        
        // 更新提交按钮状态
        function updateAccountSubmitButtonState() {
            const isUsernameValid = !usernameInput.classList.contains('error') && usernameInput.value.trim() !== '';
            const isPasswordValid = !passwordInput.classList.contains('error') && passwordInput.value.trim() !== '';
            
            submitButton.disabled = !(isUsernameValid && isPasswordValid);
        }
    }
    
    // 切换错误提示
    function toggleError(input, isValid, errorMessage) {
        const formGroup = input.closest('.form-group');
        const errorElement = formGroup.querySelector('.error-message');
        
        if (!isValid) {
            if (!errorElement) {
                const error = document.createElement('div');
                error.className = 'error-message';
                error.textContent = errorMessage;
                formGroup.appendChild(error);
            } else {
                errorElement.textContent = errorMessage;
                errorElement.style.display = 'block';
            }
            input.classList.add('error');
        } else {
            if (errorElement) {
                errorElement.style.display = 'none';
            }
            input.classList.remove('error');
        }
    }
}

/**
 * 初始化会员中心功能
 */
function initMemberCenter() {
    const memberCenter = document.querySelector('.member-center');
    const sidebarLinks = memberCenter.querySelectorAll('.sidebar-menu a');
    const tabLinks = memberCenter.querySelectorAll('.tab-link');
    
    // 侧边栏菜单切换
    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有菜单项的活跃状态
            sidebarLinks.forEach(item => item.classList.remove('active'));
            
            // 设置当前菜单项为活跃
            this.classList.add('active');
            
            // 获取目标内容区域
            const targetId = this.getAttribute('data-target');
            const targetContent = document.getElementById(targetId);
            
            // 隐藏所有内容区域
            const contentSections = memberCenter.querySelectorAll('.content-section');
            contentSections.forEach(section => section.style.display = 'none');
            
            // 显示目标内容区域
            if (targetContent) {
                targetContent.style.display = 'block';
            }
        });
    });
    
    // 标签页切换
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 获取父级标签容器
            const tabContainer = this.closest('.tabs');
            
            // 移除同组中所有标签的活跃状态
            tabContainer.querySelectorAll('.tab-link').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 设置当前标签为活跃
            this.classList.add('active');
            
            // 获取目标内容区域
            const targetId = this.getAttribute('data-target');
            const targetContent = document.getElementById(targetId);
            
            // 隐藏同组中所有内容区域
            const tabContents = tabContainer.nextElementSibling.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.style.display = 'none');
            
            // 显示目标内容区域
            if (targetContent) {
                targetContent.style.display = 'block';
            }
        });
    });
    
    // 初始化发布商品按钮
    const publishBtn = memberCenter.querySelector('.publish-btn');
    if (publishBtn) {
        publishBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 模拟跳转到发布页面
            showMessage('即将跳转到发布闲置页面...', 'info');
            
            // 实际应用中这里会跳转到发布页面
            // window.location.href = 'publish.html';
        });
    }
    
    // 初始化商品操作按钮
    const productActionBtns = memberCenter.querySelectorAll('.product-action');
    productActionBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const action = this.getAttribute('data-action');
            const productId = this.closest('.product-card').getAttribute('data-id') || '0';
            
            switch (action) {
                case 'edit':
                    showMessage(`编辑商品 ID: ${productId}`, 'info');
                    break;
                case 'delete':
                    if (confirm('确定要删除这个商品吗？')) {
                        showMessage(`删除商品 ID: ${productId}`, 'success');
                        // 在实际应用中，这里会发送删除请求并移除商品卡片
                        // this.closest('.product-card').remove();
                    }
                    break;
                case 'renew':
                    showMessage(`刷新商品 ID: ${productId}`, 'success');
                    break;
                case 'offline':
                    showMessage(`下架商品 ID: ${productId}`, 'success');
                    break;
                case 'online':
                    showMessage(`上架商品 ID: ${productId}`, 'success');
                    break;
            }
        });
    });
}

/**
 * 显示消息提示
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型：success, error, info, warning
 */
function showMessage(message, type = 'info') {
    // 检查是否已存在消息容器
    let messageContainer = document.querySelector('.message-container');
    
    if (!messageContainer) {
        // 创建消息容器
        messageContainer = document.createElement('div');
        messageContainer.className = 'message-container';
        document.body.appendChild(messageContainer);
    }
    
    // 创建消息元素
    const messageElement = document.createElement('div');
    messageElement.className = `message message-${type}`;
    messageElement.textContent = message;
    
    // 添加关闭按钮
    const closeBtn = document.createElement('span');
    closeBtn.className = 'message-close';
    closeBtn.innerHTML = '&times;';
    closeBtn.addEventListener('click', function() {
        messageContainer.removeChild(messageElement);
    });
    
    messageElement.appendChild(closeBtn);
    messageContainer.appendChild(messageElement);
    
    // 自动关闭
    setTimeout(() => {
        if (messageElement.parentNode === messageContainer) {
            messageContainer.removeChild(messageElement);
        }
    }, 5000);
}